#!/bin/bash

# TruBackend CopilotKit Integration Startup Script
# Uruchamia pełną integrację AI/ML z CopilotKit

set -e

echo "🚀 Starting TruBackend CopilotKit Integration..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[COPILOTKIT]${NC} $1"
}

print_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

print_status "Node.js is available ✅"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_status "npm is available ✅"

# Navigate to CopilotKit integration directory
cd /home/<USER>/HVAC/TruBackend/copilotkit-integration

print_header "Setting up CopilotKit Integration environment..."

# Check if package.json exists
if [ ! -f package.json ]; then
    print_error "package.json not found. Please ensure you're in the correct directory."
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d node_modules ]; then
    print_header "Installing CopilotKit dependencies..."
    npm install
    print_status "Dependencies installed ✅"
else
    print_header "Checking for new dependencies..."
    npm install
    print_status "Dependencies updated ✅"
fi

# Check if .env file exists
if [ ! -f .env ]; then
    print_header "Creating environment file..."
    cp .env.example .env
    print_warning "Please edit .env file with your API keys before proceeding"
    print_warning "Required keys: NEXT_PUBLIC_COPILOTKIT_API_KEY, OPENAI_API_KEY"
    print_warning "Press Enter to continue after editing .env file..."
    read
fi

print_status "Environment configuration ready ✅"

# Check if TruBackend services are running
print_header "Checking TruBackend services..."

services=(
    "8000:Email Intelligence Orchestrator"
    "8001:Email Integration Service"
    "8002:Langchain Automation"
    "8003:Executive AI Assistant"
    "8004:Memory Bank Service"
    "8005:Bielik Integration"
)

all_services_running=true

for service in "${services[@]}"; do
    port=$(echo $service | cut -d':' -f1)
    name=$(echo $service | cut -d':' -f2)

    if curl -s "http://localhost:$port/health" > /dev/null 2>&1; then
        print_status "$name (port $port) is running ✅"
    else
        print_warning "$name (port $port) is not responding ⚠️"
        all_services_running=false
    fi
done

if [ "$all_services_running" = false ]; then
    print_warning "Some TruBackend services are not running."
    print_warning "Would you like to start TruBackend services first? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        print_header "Starting TruBackend services..."
        cd /home/<USER>/HVAC/TruBackend
        ./start-trubackend.sh
        cd /home/<USER>/HVAC/TruBackend/copilotkit-integration
        print_status "TruBackend services started ✅"
    else
        print_warning "Continuing without all services. Some features may not work."
    fi
fi

# Build the application
print_header "Building CopilotKit Integration..."
npm run build

if [ $? -eq 0 ]; then
    print_status "Build completed successfully ✅"
else
    print_error "Build failed. Please check the errors above."
    exit 1
fi

# Start the development server
print_header "Starting CopilotKit Integration development server..."

# Check if port 3000 is available
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null ; then
    print_warning "Port 3000 is already in use. Trying to kill existing process..."
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# Start the server in background
npm run dev &
SERVER_PID=$!

# Wait for server to start
print_header "Waiting for server to start..."
sleep 10

# Check if server is running
if curl -s "http://localhost:3000" > /dev/null 2>&1; then
    print_success "CopilotKit Integration is running! ✅"
else
    print_error "Server failed to start properly"
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi

# Display service information
print_header "TruBackend CopilotKit Integration is ready!"
echo ""
echo "🌐 Frontend Application:     http://localhost:3000"
echo "🤖 CopilotKit Runtime API:   http://localhost:3000/api/copilotkit"
echo "🎨 Super Dashboard:          Enhanced UI with dual view modes"
echo "📊 TruBackend Services:"
echo "   📧 Email Intelligence:    http://localhost:8000"
echo "   📨 Email Integration:     http://localhost:8001"
echo "   🔗 Langchain Automation:  http://localhost:8002"
echo "   🤖 Executive Assistant:   http://localhost:8003"
echo "   🧠 Memory Bank:           http://localhost:8004"
echo "   🔮 Bielik Integration:    http://localhost:8005"
echo "   🛠️ MCP Tools Server:      http://localhost:8006"
echo ""

# Display usage instructions
print_header "Usage Instructions:"
echo ""
echo "🎨 Super Dashboard Features:"
echo "   - Toggle between Dashboard and Classic view modes"
echo "   - Real-time system monitoring with live metrics"
echo "   - Interactive service status cards"
echo "   - Fullscreen mode for presentations"
echo "   - AI sidebar with always-available assistant"
echo ""
echo "1. 📧 Email Analysis:"
echo "   - Use the test email form on the dashboard"
echo "   - Or ask the AI: 'Analyze this email: [paste content]'"
echo ""
echo "2. 🔍 Memory Bank Search:"
echo "   - Ask: 'Search memory bank for customer insights'"
echo "   - Or: 'Find similar HVAC issues'"
echo ""
echo "3. 🤖 Bielik V3 Consultation:"
echo "   - Ask: 'What does Bielik think about this issue?'"
echo "   - Or: 'Analyze this Polish email'"
echo ""
echo "4. 💬 Response Generation:"
echo "   - Ask: 'Generate a professional response'"
echo "   - Or: 'Create urgent response with scheduling'"
echo ""
echo "5. 📊 System Status:"
echo "   - Ask: 'Check TruBackend system status'"
echo "   - Or: 'Are all AI services running?'"
echo ""

print_success "TruBackend CopilotKit Integration is fully operational! 🎯"
print_status "Monitor logs with: npm run dev (if not already running)"
print_status "Stop services with: Ctrl+C or kill $SERVER_PID"

echo ""
echo "🌟 Where Email Intelligence Meets AI Excellence! 🌟"

# Keep the script running to show logs
wait $SERVER_PID