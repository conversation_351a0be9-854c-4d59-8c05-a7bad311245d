# Contributing to CopilotKit Hacktoberfest

⭐ Thank you for your interest in contributing!

Here’s how you can participate during Hacktoberfest:

## How to Contribute in 4 Easy Steps

1. **Pick an Issue**: Choose [GitHub Issues](https://github.com/CopilotKit/CopilotKit/issues) tagged `hacktoberfest` from our issues list and ask to be assigned.
2. **Fork and Clone**: Fork and clone the [CopilotKit repository](https://github.com/CopilotKit/CopilotKit) to your local machine.

3. **Create a Branch**: Create a branch for your changes.
4. **Develop**: Work on your assigned issue. Be sure to sync your branch with the main branch frequently to ensure compatibility.

### Preparing Your Submission

1. **Copy the Template**: Navigate to the [TEMPLATE.md](./TEMPLATE.md) file in the repository. Copy its content as the baseline for your submission.
2. **Add Your Info**: Fill out the template with details about your project:

   - **Project Title**:
   - **Description**: Purpose and value of your demo.
   - **Technologies Being Used**: List technologies and frameworks used.
   - **App Link**: If live, include the link.
   - **Screenshot**: Attach a screenshot of your demo.
   - **Detailed Description**: Explain functionality and features.
   - **Who Are You?**: Introduce yourself and your motivation.
   - **Additional Resources/Info**: Link to any extra resources or information.

3. **Create a Demo's File**: Save the pre-filled [template](./TEMPLATE.md) as a Markdown file (.md) in the `demos` folder. Name the file after your project, in lowercase.

## Submitting Your Contribution

1. **Pull Request**: Submit a pull request (PR) with your project file in the `demos` [directory](../../community/). Ensure your PR description links to the issue you addressed.
2. **Code Review**: Wait for feedback from the project maintainers and make any required revisions.
3. **Merge**: After approval, your changes will be merged with the `hacktoberfest-accepted` label to ensure you will receive credit from the official Hacktoberfest organization.

## Need Help?

- **Questions**: Use our [Discord support channel](https://discord.com/invite/6dffbvGU3D) for any questions you have.
- **Resources**: Visit [CopilotKit documentation](https://docs.copilotkit.ai/what-is-copilotkit) for more helpful documentatation info.

⭐ Happy coding and we look forward to your contributions!
