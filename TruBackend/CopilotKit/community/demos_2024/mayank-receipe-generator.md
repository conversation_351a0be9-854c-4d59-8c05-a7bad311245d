
# 1. Recipe Generator 
In this project, I developed a Recipe Generator and Suggestion App using React for the frontend, Tailwind CSS for styling, and Shadcn UI for enhanced UI components.
- Recipe Generation:
Users can add a recipe by entering the dish name. The app automatically generates ingredients, instructions, and tips using an internal logic (or AI-based integration).4

- Recipe Suggestions:
Users can input available ingredients, and the app will suggest recipes based on matching ingredients using predefined logic.

## 2. Name of Issue/Topic

⭐ 02 - Recipe Generator (Hacktoberfest Demo)  #603

## 3. Technologies Being Used

List the technologies and frameworks you used (e.g., CopilotKit, Next.js)
- [Next.js](https://nextjs.org)
- [Tailwind CSS](https://tailwindcss.com)
- [CopilotKit](https://copilotkit.ai)
- [ShadCN](https://ui.shadcn.com)

### Note: Please only use the [Shadcn/ui]([https://v0.dev/docs](https://ui.shadcn.com/docs/installation)) for styling components.

- Shadcn-UI Component Styling
- CopilotKit

### GitHub Repo Link: 
https://github.com/Mayank77maruti/receipeGenerator

### 5 Live Link

- [Live app](https://receipe-generator-1zyq.vercel.app/)
 
## 7. Who am i?

Please list your **GitHub** and **Twitter** handles if you feel comfortable doing so. 

- [github](https://github.com/Mayank77maruti)
- [X](https://x.com/MayankMoha31086)

## 8. Posts

- A dev.to blog link - [Link](https://dev.to/mayank_mohapatra/recipe-generator-powered-by-copilotkit-chatbot-1mlg)
- Tweet link - [Link](https://x.com/MayankMoha31086/status/1851935687018508434)

