# 1. CopilotMate
CopilotMate: An AI-powered productivity assistant with tools like to-do lists, expense tracking, StudyBuddy with coagent, ai spreadsheet and more.

### 2. Technologies Being Used  
<p align="center">
  <img src="https://img.shields.io/badge/Next.js-black?logo=next.js" alt="Next.js" />
  <img src="https://img.shields.io/badge/Tailwind_CSS-38B2AC?logo=tailwind-css&logoColor=white" alt="Tailwind CSS" />
  <img src="https://img.shields.io/badge/CopilotKit-🪁-black" alt="CopilotKit" />
  <img src="https://img.shields.io/badge/Framer%20Motion-0055FF?logo=framer&logoColor=white" alt="Framer Motion" />
</p>

- **CopilotKit** (for AI functionalities)
- **Next.js** (for the frontend)
- **Tailwind CSS** (for styling)
- **Groq SDK** (for language models)
- **Llama model** (for AI coagents)

### 3. GitHub Project Link  
GitHub Repo Link:  [Copilotmate Repo](https://github.com/AkashJana18/copilotmate)


### 4. Deployment 
 Deploying soon....Stay tuned. Till then watch [Demo](https://www.youtube.com/watch?v=qPVRPUH8ewU&t=33s)

### 5. Screenshot 
![image](https://github.com/user-attachments/assets/68df962c-9bc2-48ae-9ffd-3c073f13e083)
![image](https://github.com/user-attachments/assets/5d78657c-fa78-492c-bbf8-d2dcdf6fa699)
![image](https://github.com/user-attachments/assets/1fe3ea5e-4a34-4755-a042-d327adc17c8e)
![image](https://github.com/user-attachments/assets/0f1fa245-10bb-441c-be77-162a04f941d8)
![image](https://github.com/user-attachments/assets/10814ebf-95ed-4520-966c-5e5854c46335)
![image](https://github.com/user-attachments/assets/cee8e3ca-ecfa-43d1-adf0-8f8972c4c14b)
![image](https://github.com/user-attachments/assets/39e2087c-0f7d-4a69-9fbe-e30415fd7cbe)
![image](https://github.com/user-attachments/assets/9919059e-71ce-4bb0-9f50-cc9f351078be)
![image](https://github.com/user-attachments/assets/0ccd162a-c4f7-4d79-a969-a124222f5bf6)
![image](https://github.com/user-attachments/assets/cb118098-d975-41fe-b1d8-0387d312ca32)
![image](https://github.com/user-attachments/assets/d5ba6011-71e3-4f7b-8566-e27cc83c35ac)

#### ⭐ Please watch the video to feel the true UI/UX : [Demo](https://www.youtube.com/watch?v=qPVRPUH8ewU&t=33s)

### 6. Who Are You?  
- **GitHub**: [Akash's GitHub](https://github.com/AkashJana18)  
- **Twitter**: [Akashj_01](https://x.com/Akashj_01)
- **LinkedIn**: [Akash Jana](https://linkedin.com/u/akashjana)

### 7. Activities
Link to any bonus activities:  
- Solved GitHub issues related to CopilotKit :
  - Fixes #713, #645, #644, #626 (Hacktober related)
  - Fixed #726 [Link](https://github.com/CopilotKit/CopilotKit/pull/726)
- Posted about CopilotMate on
  1. [Twitter](https://x.com/Akashj_01/status/1843662122917736475)
  2. [Linkedin](https://www.linkedin.com/feed/update/urn:li:activity:7254023926891065346/)
- Published an article on Dev.to:
  1. https://dev.to/akashjana/future-of-productivity-meet-copilotmate-3k7i
  2. https://dev.to/akashjana/how-i-integrated-copilotkit-ai-into-copilotmate-23gm
  3. https://dev.to/akashjana/my-hacktoberfest-experience-a-journey-of-code-challenges-and-community-1b9p
