## 🚀 **Project Title**

[Meal Prep Planner](https://meal-prep-planner.vercel.app/) - An AI powered meal planning app that prepares meal plans based on user preferences and nutritional requirements.

Meal Prep Planner is an AI powered meal planning app that prepares meal plans based on user preferences and nutritional requirements. The app uses AI algorithms to suggest meals based on user preferences and nutritional requirements.

### 📝 **Name of Issue/Topic**

Meal Prep Planner ( Hacktoberfest Demo )

---

### 🛠️ **Technologies Being Used**

- **Frameworks**: Next.js and TypeScript for types
- **Libraries**: React.js
- **Styling**: TailwindCSS, Shadcn-UI Component Styling
- **Developer Tools**: CopilotKit and Groq

---

### 🌐 **App Link**
  
- [MINI-FAQ-BOT Live Demo](https://meal-prep-planner.vercel.app/)
- [Github Repo](https://github.com/AJBrownson/meal-prep-planner)

---

### 🎯 **Bonus Points**

- [Twitter Post](https://x.com/TechieAnni/status/1847802763083673644)

---

### 📸 **Screenshot**

![App Screenshot](https://github.com/user-attachments/assets/1e5e779b-73ef-4d71-ac8d-ab66fa1af483)

---

### 🙋‍♂️ **Who Are You?**

- [Anietie Brownson](https://github.com/AJBrownson)
- [Anietie Brownson on X](https://x.com/TechieAnni)
