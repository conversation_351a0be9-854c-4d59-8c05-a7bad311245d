## Legal Document Reviewer

## ⭐ 42 - Legal Document Reviewer (Hacktoberfest Demo)

Develop a system where AI agents analyze legal documents, highlight important sections, and suggest revisions to ensure compliance and clarity.

## Technologies Being Used

- **CopilotKit**: This project must utilize CopilotKit as a core component.
- **React.js**: A JavaScript library for building user interfaces.
- **Next.js**:  A React framework that enables server-side rendering and static site generation.
- **Developer Tools**: CopilotKit powered with Gemini
- **Shadcn-UI**: A component library for building user interfaces with React.
- **Tailwind CSS**: A utility-first CSS framework for styling applications.
- **TypeScript**: A typed superset of JavaScript (if you are using it).

### GitHub Repo Link: 
https://github.com/ARYPROGRAMMER/Legal-Document-Reviewer

- [Live Demo](https://legal-document-reviewer.vercel.app/)

## Screenshot

![image](https://github.com/ARYPROGRAMMER/Legal-Document-Reviewer/blob/master/screenshots/img2.png)
![image](https://github.com/ARYPROGRAMMER/Legal-Document-Reviewer/blob/master/screenshots/img3.png)


## 7. Who am I?

- [Github](https://github.com/ARYPROGRAMMER)
- [Linkedin](https://www.linkedin.com/in/its-arya/)