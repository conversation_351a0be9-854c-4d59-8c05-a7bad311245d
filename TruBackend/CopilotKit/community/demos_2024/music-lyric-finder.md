## 🚀 **Project Title**

[Music Lyric Finder](https://music-lyric-finder.vercel.app/) - This is a bot that helps users find the lyrics to their favorite songs. It finds and displays lyrics to any song based on user input.

### 📝 **Name of Issue/Topic**

Music Lyric Finder ( Hacktoberfest Demo )

---

### 🛠️ **Technologies Being Used**

- **Frameworks**: Next.js and TypeScript for types
- **Libraries**: React.js
- **Styling**: TailwindCSS, Shadcn-UI Component Styling
- **Developer Tools**: CopilotKit and Groq

---

### 🌐 **App Link**

- [Music Lyric Finder Live Demo](https://music-lyric-finder.vercel.app/)
- [Github Repo](https://github.com/AJBrownson/music-lyric-finder)

---

### 🎯 **Bonus Points**

- [Twitter Post](https://x.com/TechieAnni/status/1849311489225597146)

---

### 📸 **Screenshot**

![App Initial Screenshot](https://github.com/user-attachments/assets/40857ef5-51a1-4c11-8f59-c0ea231b797a)
![Search Results Screenshot](https://github.com/user-attachments/assets/d6da7992-3517-4d61-a929-29f836208411)

---

### 🙋‍♂️ **Who Are You?**

- [Anietie Brownson](https://github.com/AJBrownson)
- [Anietie Brownson on X](https://x.com/TechieAnni)