## Project Planner

[Event Planner](https://event-planner-gfex.vercel.app/) - An Event Planner with Copilot and Shadcn-UI.

The Event Planner web application allows users to manage events with features like creating, editing, and deleting events. It includes a user-friendly interface displaying event details such as title, description, date, time, location, category, and priority. Events are displayed in card format, making it easy to view and interact with. Additionally, the app integrates Copilot Assistant, which aids users by providing suggestions and automating certain tasks.

It's built using modern web technologies such as React, Next.js, TailwindCSS, Shadcn-UI and integrates a backend API for event management.

## Name of Issue/Topic

05 - Event Planner (Hacktoberfest Demo)

## Technologies Being Used

- Next.js: React Framework
- React: JavaScript library for building user interfaces
- CopilotKit: Ai Copilots
- TypeScript: JavaScript with types
- TailwindCSS: utility-first CSS framework
- Shadncn: UI Component Styling

## App Link

- [Snippy Live Demo](https://event-planner-gfex.vercel.app/)
- [Repo](https://github.com/Zedoman/Event-Planner)


## Bonus Points

Dev.to[https://dev.to/avradeep_nayak_fa8d5f6995] -> https://dev.to/avradeep_nayak_fa8d5f6995/event-planner-3k2
Medium -> https://medium.com/@12346arjo/event-planner-abfba6a2eadb

## Screenshots

### create, update, and delete Event
<img width="1657" alt="Screenshot 2024-10-04 at 18 54 50" src="https://github.com/user-attachments/assets/999200d5-5e1e-4f48-a293-477f3e68f08a">
<img width="1680" alt="Screenshot 2024-10-04 at 18 54 38" src="https://github.com/user-attachments/assets/0dfe4ae5-9a7a-4cf4-bfb8-f078e39c2ab8">
<img width="1635" alt="Screenshot 2024-10-04 at 18 53 46" src="https://github.com/user-attachments/assets/5ca40f69-c1ca-4585-840a-984d05ce755c">



### Home Page
<img width="1632" alt="Screenshot 2024-10-04 at 18 56 48" src="https://github.com/user-attachments/assets/3d98cf1c-b9e3-4b00-9460-45e186989f59">



### CopilotChat chat

<img width="689" alt="Screenshot 2024-10-04 at 18 57 24" src="https://github.com/user-attachments/assets/d05207f8-7ffb-4367-b96b-53157957dc00">


## Who Are You?

- [Avradeep](https://github.com/Zedoman)
- [Avradeep on Linkedin](https://www.linkedin.com/in/avradeep-nayak-7604b5222/)