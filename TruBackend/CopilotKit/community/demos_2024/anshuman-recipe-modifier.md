
## 1. Project Title:- Recipe-Modifier

## 2. Name of Issue/Topic:-13 - Recipe Modifier (Hacktoberfest Demo)

https://github.com/CopilotKit/CopilotKit/issues/625


## 3. Technologies Being Used

- v0 Styling
- Shadcn-UI Component Styling
- CopilotKit

## 4. GitHub Project Link

### GitHub Repo Link: 

https://github.com/anshuman-rai-27/Recipe_Modifier

### 5 bonus points

[Live Demo](https://recipe-modifier.vercel.app/)


## 6. Screenshot

![image](https://github.com/user-attachments/assets/e024ddda-8d3c-4646-b0f8-7c08c8b366c7)


![image](https://github.com/user-attachments/assets/dbe82fe7-4790-4979-876f-cdd96651701b)

## 7. Who Are You?

[Github](https://github.com/anshuman-rai-27)
[Twitter](https://x.com/Anshuman_rai_?t=jOqgQuKILlsoA7ERNt_7Kw&s=08 )

## 5 Extra Bonus Points

[Tweet Link](https://x.com/Anshuman_rai_/status/1850235729089343706?t=lyeygtrh8uZkqiq0bPbSOw&s=08)
[Dev.to](https://dev.to/anshumanrai27/building-a-recipe-modifier-tool-with-nextjs-react-and-copilotkit-383j)