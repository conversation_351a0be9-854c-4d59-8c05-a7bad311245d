## **News Aggregator**
News Aggregator uses a large language model with the copilot kit eco-system to generate a summary of news and happenings based on specified topics keywords or even provided articles.
### 📝 **Name of Issue/Topic**

 15 - News Aggregator (Hacktoberfest Demo)

### 🛠️ **Technologies Used**

- **Frameworks**:  Node.js and React.js
- **Styling**: v0 Styling, Shadcn-UI Component Styling, 
- **Developer Tools**: CopilotKit, groq-sdk



---

### 🌐 **App Link**

[Live Demo](https://news-aggregator-sharp.vercel.app/)


### 🎯 **Bonus Points**

- [Post on X](https://x.com/f_adex_/status/1851187171979399263?s=46)

---

### 📸 **Screenshot**


![alt text](image.png)

---

### 🙋‍♂️ **Who Are You?**

- [Fadex on Github](https://github.com/fadexadex)

- [Fadex on Linkedin](https://linkedin.com/in/danie<PERSON><PERSON><PERSON>)
