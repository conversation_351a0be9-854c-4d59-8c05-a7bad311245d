## 1. Flashcard and Quiz Generator Bot
In this project, I developed an interactive Flashcard and Quiz Generator App using React, styled with Tailwind CSS, and enhanced with Shadcn UI components for a polished user experience.

- Flashcard Generation:
Users can create flashcards by entering a topic or concept. The app automatically generates flashcards with key terms, definitions, and helpful examples through internal logic or AI-based integration.

- Interactive Quiz Generation:
Users can generate quizzes based on a specific topic or concept. The app uses predefined logic (or AI) to create adaptive quizzes, delivering questions that help users assess and reinforce their knowledge.

## 2. Name of Issue/Topic

⭐ 17 - Interactive Flashcards (Hacktoberfest Demo) #629

## 3. Technologies Being Used

List the technologies and frameworks you used (e.g., CopilotKit, Next.js)
- [Next.js](https://nextjs.org)
- [Tailwind CSS](https://tailwindcss.com)
- [CopilotKit](https://copilotkit.ai)
- [ShadCN](https://ui.shadcn.com)

## 4. GitHub Project Link

- [REPO](https://github.com/sanketshinde3001/Flashcards-and-Quiz)

## 5 Live Link

- [Live app](https://ai-flashcards-free.vercel.app/)
 
## 6. Screenshot

![image](https://github.com/user-attachments/assets/114bc863-1330-4af1-bb3c-b7b6c9b98487)

![image](https://github.com/user-attachments/assets/5c6e309e-eff5-4b66-a400-4fbf0777b52c)

![image](https://github.com/user-attachments/assets/81de24b6-0cbb-47f2-9b1b-0f74cfc42ab8)

## 7. Who am i?

Please list your **GitHub** and **Twitter** handles if you feel comfortable doing so. 

- [sanket on github](https://github.com/sanketshinde3001)
- [sanket on X](https://x.com/sanketshinde04)

## 8. Posts

- A dev.to blog link - [Link](https://dev.to/sanketshinde/building-an-interactive-flashcard-and-quiz-generator-app-be8)
- Tweet link - [Link](https://x.com/sanketshinde04/status/1850229190047482138)
- Youtube Video - [Video](https://youtu.be/mDBC9xU1b-E)
