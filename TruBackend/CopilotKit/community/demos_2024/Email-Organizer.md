## 🚀 **Project Title** :- Hacktoberfest Demo

### 📝 **Name of Issue/Topic**:- Email Organizer 

Brief Description:-
The Email Organizer project addresses the challenge of managing email overload by automatically categorizing emails based on urgency, sender, and content type. With the rise of communication through emails, users often struggle with a cluttered inbox, making it difficult to prioritize important messages. This project streamlines the process by analyzing email content and sorting it into relevant categories, allowing users to focus on what matters most and improving overall productivity.

---

### 🛠️ **Technologies Being Used**

List all technologies, tools, and frameworks you are utilizing for the project:

- **Frameworks**:  Next.js
- **Backend Tools**: OpenAI API for email categorization
- **Styling**: Tailwind CSS for responsive design, Shadcn-UI for component styling
- **Developer Tools**: CopilotKit

> **Note**: Ensure consistency by adhering to the [v0 guidelines](https://v0.dev/docs).

---

### 🌐 **App Link**

If your app is live, include the link here:  
Live Demo([Email-Organizer](https://email-organizer-7vs97j141-rishikesh-maddhesiyas-projects.vercel.app/))



---

### 🎯 **Bonus Points**


- good first issue (https://github.com/Rishikesh63/Email-Organizer/issues/1)
- posts on Twitter([posts on Twitter](https://x.com/Rishikeshmaddh7/status/1848668826634011063))
---

### 📸 **Screenshot**

Include a screenshot of your demo in action:  
(![Screenshot 2024-10-22 194003](https://github.com/user-attachments/assets/413e9b5e-3fc8-4cc4-9ba8-6eb83e0f0a01)
![Screenshot 2024-10-22 194047](https://github.com/user-attachments/assets/766f725f-bd39-4e65-b76d-8001fc6dd869)
![Screenshot 2024-10-22 193907](https://github.com/user-attachments/assets/a2e6a51c-4ade-4864-bba6-b3f95c3ac94a)


---

### 🙋‍♂️ **Who Are You?**
- My Github Profile([Rishikesh Maddhesiya](https://github.com/Rishikesh63))
- My Linkedn Profile([Rishikesh Maddhesiya](https://www.linkedin.com/in/rishikesh-maddheshiya-84052b211/))
- My Twitter Profile([Rishikesh Maddhesiya](https://x.com/Rishikeshmaddh7))
  
