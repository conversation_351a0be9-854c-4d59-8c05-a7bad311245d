## Customer Bot

[SwiftSupport](https://customer-bot-three.vercel.app/) - A Real-Time AI-Powered Chatbot for Customer Support

This Customer Support Chatbot is designed to assist Customers in providing AI-driven customer service. It leverages the Google Generative AI model via CopilotKit to handle customer queries in real-time, offering automated and intelligent responses.

## Name of Issue/Topic

39 - Customer Support Bot (Hacktoberfest Demo) #786

## Technologies Being Used

- Express.js: Backend framework for Node.js
- React: Frontend JavaScript library for building user interfaces
- CopilotKit: AI copilots integration for advanced features
- Google Generative AI: AI model for generating intelligent responses
- Axios: For handling API requests
- TypeScript: Strongly typed JavaScript for better development experience

## App Link

- [SwiftSupport Live Demo](https://customer-bot-three.vercel.app/)
- [Repo](https://github.com/Zedoman/Customer_Bot)

## Bonus Points

Link to any bonus activities you did such as solving GitHub Issues or posting to Twitter, Dev.to, Medium, etc.
Dev.to[https://dev.to/avradeep_nayak_fa8d5f6995] -> https://dev.to/avradeep_nayak_fa8d5f6995/swiftsupport-239m

## Screenshots

### Chatbot UI for Customer Support
<img width="1677" alt="Screenshot 2024-10-27 at 14 42 24" src="https://github.com/user-attachments/assets/3a589614-b302-4c35-bd13-9f3aec483adf">


### Real-time conversation with AI Copilot responses
<img width="1667" alt="Screenshot 2024-10-27 at 14 47 24" src="https://github.com/user-attachments/assets/e33a366f-1dd0-4c20-86fa-13fd23d114ba">


## Who Are You?

- [Avradeep](https://github.com/Zedoman)
- [Avradeep on Linkedin](https://www.linkedin.com/in/avradeep-nayak-7604b5222/)
