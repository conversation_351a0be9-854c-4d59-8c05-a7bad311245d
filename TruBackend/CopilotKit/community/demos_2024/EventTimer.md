
# Event Timer with <PERSON><PERSON><PERSON>
In this project, I developed an Event Countdown Timer with Copi<PERSON> Bo<PERSON> integration using React for the frontend, Tailwind CSS for styling, and the CopilotKit for interactive guidance and suggestions.

- Event Timer: Users can add an event with a specific target date and time. The app displays a countdown for the event, and users can manage multiple events by adding or removing them from the list.
- Copilot Bot Integration: The app is integrated with Copilot Bot to assist users with instructions on how to set timers, offering help for timer creation in a user-friendly way.

## 2. Name of Issue/Topic

⭐ 40 - Event Timer with <PERSON><PERSON><PERSON> <PERSON><PERSON> (Hacktoberfest Demo) #787

## 3. Technologies Being Used

List the technologies and frameworks you used (e.g., CopilotKit, Next.js)
- [Next.js](https://nextjs.org)
- [Tailwind CSS](https://tailwindcss.com)
- [CopilotKit](https://copilotkit.ai)


## 4. GitHub Project Link

### GitHub Repo Link: 
[https://github.com/prathamesh424/EventCounter--Copilotkit](https://github.com/prathamesh424/EventCounter--Copilotkit)

### 5 bonus points

- If your app is live, include the link here:
- [Live app](https://event-timer-one.vercel.app/)
 
## 6. Screenshot


![Screenshot 2024-10-26 175826](https://github.com/user-attachments/assets/23ceae41-6d87-4894-b5e5-79892da63586)

![Screenshot 2024-10-26 175846](https://github.com/user-attachments/assets/387fd983-041b-44b1-b51b-060ac303b112)


## 7. Who Are You?

Please list your **GitHub** and **Twitter** handles if you feel comfortable doing so. 

- [prathamesh424 on github](https://github.com/prathamesh424)
- [sanket on X](https://x.com/Prathamesh_G24)

## 5 Extra Bonus Points
Link to any bonus activities you did such as solving GitHub Issues or posting to Twitter, Dev.to, Medium, etc related to your project and CopilotKit.
- A dev.to blog link - [Link](https://dev.to/prathamesh_gursal/event-timer-powered-by-copilotkit-chatbot-2c78)
- Tweet link - [Link](https://x.com/Prathamesh_G24/status/1850161431477731547)

#issue 787
