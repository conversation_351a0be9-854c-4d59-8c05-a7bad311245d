# Rename this Markdown file with the name of your project and remove line #1

## 1. Project Title

## 2. Name of Issue/Topic

Provide the topic or challenge that your demo addresses.

## 3. Technologies Being Used

List the technologies and frameworks you used (e.g., CopilotKit, Next.js)

### Note: Please only use the [Shadcn/ui]([https://v0.dev/docs](https://ui.shadcn.com/docs/installation)) for styling components.

- v0 Styling
- Shadcn-UI Component Styling
- CopilotKit

## 4. GitHub Project Link

### GitHub Repo Link: 
https://github.com/NathanTarbert/my-cool-project

### 5 bonus points

- If your app is live, include the link here:
[Live Demo](http://google.com)

- Deploy your app on [Vercel](https://vercel.com/new)

## 6. Screenshot

Include a screenshot of your demo in action:
![image](https://github.com/user-attachments/assets/5d2a020c-dc8f-4b27-85db-ba1413bdc8f6)

## 7. Who Are You?

Please list your **GitHub** and **Twitter** handles if you feel comfortable doing so. 

We want to promote what you've built and your hard work

## 5 Extra Bonus Points

Link to any bonus activities you did such as solving GitHub Issues or posting to Twitter, Dev.to, Medium, etc related to your project and CopilotKit.
