## Project Title

[Cookbook](https://cookbook-eosin.vercel.app) - A Recipe Generating App

CookBook is a recipe generating app that allows users to search for recipes based on ingredients they have on hand. Users can also save recipes in their personal cookbook for future reference. CookBook uses CopilotKit to generate recipes with the help of GroqAI's API (you can use your own LLM model if you have one or try ChatGPT, Gemini, or any other model).

## Name of Issue/Topic

Recipe Generator (Hacktoberfest Demo)

## Technologies Being Used

- Next.js: React Framework
- React: JavaScript library for building user interfaces
- CopilotKit: Ai Copilots
- TypeScript: JavaScript with types
- TailwindCSS: utility-first CSS framework
- Shadncn: UI Component Styling
- v0: For AI based component generation
- Zustand: State Management

## App Link

- [Cookbook Live Demo](https://cookbook-eosin.vercel.app)
- [Repo](https://github.com/RohittCodes/cookbook)

## Bonus Points
X post: [CookBook](https://x.com/RohittCodes/status/1850148812528169220)

## Screenshots
![Cookbook-1](https://github.com/user-attachments/assets/c152ae2b-aa96-421a-b567-8af37a89253d)

![Cookbook2](https://github.com/user-attachments/assets/4591fc5b-2811-44df-b5cd-b631cdbc4420)


## Who Are You?

- [RohittCodes](https://github.com/RohittCodes)
- [RohittCodes on X](https://x.com/RohittCodes)
