## 1. 🚇 Chat w/ Web 🚇

## 2.  ✨ Name of Topic

browser extension featuring ai usability

## 3. 🥪 Technologies Being Used

- **Frontend**: [React](https://reactjs.org/) - A JavaScript library for building user interfaces, known for its component-based architecture and efficient rendering.
  
- **Backend**: [CopilotKit](https://www.copilotkit.ai/) - A framework for building custom AI copilots, including in-app chatbots and AI-powered text areas.
  
- **Framework**: [WXT](https://wxt.dev/) - A next-gen web extension framework that makes Briowser extension development faster than ever before, offering a simplified development experience.
  
- **Styling**: [Tailwind CSS](https://tailwindcss.com/) - A utility-first CSS framework that provides a set of classes to build custom designs quickly.

## 4. 🖥️ GitHub Project 

### GitHub Repo Link: 

- **https://github.com/ArnavK-09/chat-w-web**

## 5. 🎬 Video Demo

| [**🏮🏮 CLICK HERE FOR YOUTUBE VIDEO 🏮🏮**](https://youtu.be/AEDV-N_7bIY?si=FH9sfz_4cmIPvcuJ)              |
| ----------------------------------------- |
| **Youtube video includes installation process and demo of browser extension!** |

## 6. 📷 Screenshot


<p align="center">
 <img alt="demo" src="https://github.com/user-attachments/assets/f39ffd53-0f72-4492-a238-e101649f23f4" />
</p>

    
## 7. 👨‍💻 Who Are You?

- [**` ArnavK-09 `**](https://GitHub.com/ArnavK-09)
