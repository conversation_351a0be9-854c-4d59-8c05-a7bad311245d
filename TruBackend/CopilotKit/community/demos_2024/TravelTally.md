## TravelTally

[TravelTally](https://travel-budget-liart.vercel.app/) - A budgeting and expense management tool for Travel Expenses.

TravelTally is a travel budget planner that allows users to efficiently manage their finances by tracking expenses related to their trips, including airfare, lodging, and activities. Users can monitor their budgets, view how much money they have spent compared to their allocated budget, and receive assistance from a built-in Copilot assistant. The application features a clean, responsive UI built with TailwindCSS, while leveraging React and Redux for robust state management, along with CopilotKit and Shadcn-UI for enhanced functionality and styling.

## Name of Issue/Topic

24 - Travel Budget Planner (Hacktoberfest Demo)

## Technologies Being Used

- React: JavaScript library for building user interfaces
- Redux: State management library for JavaScript apps
- TailwindCSS: Utility-first CSS framework
- CircularProgressbar: For visualizing budget and expense data
- Vite: Fast and modern build tool that enhances development with native ES modules and instant hot module replacement.
- JavaScript: Primary programming language for development
- Shadcn-UI: UI component library built with accessibility and customization in mind, styled with TailwindCSS.
- CopilotKit: Ai CopilotBot

## App Link

- [TravelTally Live Demo](https://travel-budget-liart.vercel.app/)
- [Repo](https://github.com/Zedoman/Travel_Budget)

## Bonus Points

Link to any bonus activities you did such as solving GitHub Issues or posting to Twitter, Dev.to, Medium, etc.
Dev.to[https://dev.to/avradeep_nayak_fa8d5f6995] -> https://dev.to/avradeep_nayak_fa8d5f6995/traveltally-58hc

## Screenshots

### Home Page to Set your travel budget
<img width="1679" alt="Screenshot 2024-10-26 at 23 01 45" src="https://github.com/user-attachments/assets/86aaf996-1896-4222-bd53-9d230cb4bed0">


### Before setting expenses
<img width="1680" alt="Screenshot 2024-10-26 at 23 01 59" src="https://github.com/user-attachments/assets/021e6e95-0240-4f40-be59-175852219689">


### To add expenses for the new trip from the bottom + botton
<img width="1680" alt="Screenshot 2024-10-26 at 23 02 11" src="https://github.com/user-attachments/assets/d80d0708-c32f-42c5-ac0a-d80fb1d77e71">

### To see the expenses and track the budget
<img width="1677" alt="Screenshot 2024-10-26 at 23 02 33" src="https://github.com/user-attachments/assets/bbb0e403-0614-4f1b-a64e-df3d167a11fb">

### Copilot implementation
<img width="1666" alt="Screenshot 2024-10-26 at 23 08 14" src="https://github.com/user-attachments/assets/286e856e-c5e2-4630-a239-a6326b9324d1">



## Who Are You?

- [Avradeep](https://github.com/Zedoman)
- [Avradeep on Linkedin](https://www.linkedin.com/in/avradeep-nayak-7604b5222/)

