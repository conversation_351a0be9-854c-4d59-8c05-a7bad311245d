## 🚀 **Habit Tracker**  

### 📝 **Issue: 19 - Habit Tracker (Hacktoberfest Demo) #631**
Develop a simple habit tracker that allows users to log their daily habits.


---

### 🛠️ **Technologies Being Used**

- **React.js**: A JavaScript library for building user interfaces.
- **Next.js**:  A React framework that enables server-side rendering and static site generation.
- **Developer Tools**: CopilotKit powered with Gemini
- **Shadcn-UI**: A component library for building user interfaces with React.
- **Tailwind CSS**: A utility-first CSS framework for styling applications.
- **TypeScript**: A typed superset of JavaScript (if you are using it).
---

### 🌐 **App Link**
https://habittracker-umber.vercel.app/

---

### 🎯 **Repo Link**

https://github.com/AmalFrancisOlakengil/habit-tracker

---

### 📸 **Screenshot**

Include a screenshot of your demo in action:  
![image](https://raw.githubusercontent.com/AmalFrancisOlakengil/my_attachments/refs/heads/main/Screenshot%202024-10-12%20220538.png)  
![image](https://raw.githubusercontent.com/AmalFrancisOlakengil/my_attachments/refs/heads/main/Screenshot%202024-10-12%20220758.png)

---

### 🙋‍♂️ **Who Are You?**

Github: https://github.com/AmalFrancisOlakengil  
linkedin: https://www.linkedin.com/in/amalfrancisolakengil/
