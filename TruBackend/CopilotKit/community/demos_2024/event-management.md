
# Event management with <PERSON><PERSON><PERSON>
In this project, I developed an Event management with Copi<PERSON> Bo<PERSON> integration using Next Js for the frontend, Tailwind CSS for styling, and the CopilotKit for interactive guidance and suggestions.

- Event Calendar: Users can add an event in a specific target date and time. The app displays a calendar view where users can add event , and users can manage multiple events by adding or removing them from the list.
- Copilot Bot Integration: The app is integrated with Copilot Bot to assist users with instructions on how to add an event , offering help for planning event in a user-friendly way.

## 2. Name of Issue/Topic

⭐ 40 - Event Timer with <PERSON><PERSON><PERSON> <PERSON> (Hacktoberfest Demo) #787

## 3. Technologies Being Used

List the technologies and frameworks you used (e.g., CopilotKit, Next.js)
- [Next.js](https://nextjs.org)
- [Tailwind CSS](https://tailwindcss.com)
- [Shadcn](https://ui.shadcn.com)
- [CopilotKit](https://copilotkit.ai)


## 4. GitHub Project Link

### GitHub Repo Link: 
[https://github.com/heysagnik/event-manager](https://github.com/heysagnik/event-manager)

### 5 bonus points

- If your app is live, include the link here:
- [Live app](https://https://event-manager-blond.vercel.app/)
 
## 6. Screenshot


![image](https://github.com/user-attachments/assets/16674726-05b6-44a9-a303-a20032777fe6)

![image](https://github.com/user-attachments/assets/02e6e819-a767-4f03-ae76-e0719110fa94)

![image](https://github.com/user-attachments/assets/4b6e25b3-b63f-488d-a3dc-e82f8748f8df)

![image](https://github.com/user-attachments/assets/45638d20-1d80-4d2f-b00f-bd99043efeb6)




## 7. Who Are You?

Please list your **GitHub** and **Twitter** handles if you feel comfortable doing so. 

- [heysagnik on github](https://github.com/heysagnik)
- [heysagnik on X](https://x.com/heysagnik)

## 5 Extra Bonus Points
Link to any bonus activities you did such as solving GitHub Issues or posting to Twitter, Dev.to, Medium, etc related to your project and CopilotKit.
- A dev.to blog link - [Link]()
- Tweet link - [Link]()

#issue 787
