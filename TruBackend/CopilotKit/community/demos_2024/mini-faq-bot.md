## 🚀 **Project Title**

[MINI-FAQ-BOT](https://mini-faq-bot.vercel.app/) - An AI powered FAQ chatbot to answer users' frequently asked questions

Mini is an AI chatbot that reads business information stored in a markdown or json file (in this case, the information is about a fictional social media scheduling software called Teew<PERSON>ty). <PERSON> utilizes CopilotKit to read the data given in order to be able to provide informed answers to users' queries about the software.

### 📝 **Name of Issue/Topic**

AI Powered FAQ Bot ( Hacktoberfest Demo )

---

### 🛠️ **Technologies Being Used**

- **Frameworks**: Next.js and TypeScript for types
- **Libraries**: React.js
- **Styling**: TailwindCSS, Shadcn-UI Component Styling
- **Developer Tools**: CopilotKit and Groq

---

### 🌐 **App Link**
  
- [MINI-FAQ-BOT Live Demo](https://mini-faq-bot.vercel.app/)
- [Github Repo](https://github.com/AJBrownson/mini-faq-bot)

---

### 🎯 **Bonus Points**

- [Twitter Post](https://x.com/TechieAnni/status/1844512911445647413)

---

### 📸 **Screenshot**

![Welcome Message](https://github.com/user-attachments/assets/412181bf-ae0c-4782-8511-eb974477742b)
![Results](https://github.com/user-attachments/assets/94d85a40-e2c9-4b3f-b1a0-c51854bae978)
![Results](https://github.com/user-attachments/assets/e70bef77-5478-4fd2-9cca-95ee1c5a3b06)

---

### 🙋‍♂️ **Who Are You?**

- [Anietie Brownson](https://github.com/AJBrownson)
- [Anietie Brownson on X](https://x.com/TechieAnni)
