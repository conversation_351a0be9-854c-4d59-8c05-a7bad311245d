## 1. Project Title
QuizFlip- A app that has generates flashcards on the topic that you ask for

## 2. Name of Issue/Topic

Interactive Flashcards (Hacktoberfest Demo)

## 3. Technologies Being Used

- Next.js: React Framework
- React: JavaScript library for building user interfaces
- CopilotKit: Ai Copilots
- TypeScript: JavaScript with types
- TailwindCSS: utility-first CSS framework
- Shadncn: UI Component Styling
- Zustand: State Management

### Note: Please only use the [Shadcn/ui]([https://v0.dev/docs](https://ui.shadcn.com/docs/installation)) for styling components.

- Shadcn-UI Component Styling
- CopilotKit

## 4. GitHub Project Link

- [QuizFlip Live Demo](https://learn-using-flash-cards.vercel.app/)
- [Repo](https://github.com/Niharika0104/learn-using-flash-cards)

### GitHub Repo Link: 

https://github.com/Niharika0104/learn-using-flash-cards

### 5 bonus points

- [QuizFlip Live Demo](https://learn-using-flash-cards.vercel.app/)
- [CopilotKit Tutorial](https://dev.to/niharikaa/integrate-ai-effortlessly-a-beginners-guide-to-using-copilotkit-1pgg)
## 6. Screenshot
- Dashboard
![image](https://github.com/user-attachments/assets/e8fb024c-76d2-4efa-912b-645b8ee58d91)
- CopilotChat
![image](https://github.com/user-attachments/assets/ba4daad8-9fb7-458e-8114-f60a28f264d0)
- Learn Page
![image](https://github.com/user-attachments/assets/025a6f40-64a0-46ab-b9d1-982fc509406e)
- Quiz Page
![image](https://github.com/user-attachments/assets/1e1f7d09-15a3-4512-8ec7-accb6a8a14b7)
- Result Page
![image](https://github.com/user-attachments/assets/23cc374c-2bed-4ce6-af93-62732bbe7bb2)


## 7. Who Are You?

[Niharika0104](https://github.com/Niharika0104)
