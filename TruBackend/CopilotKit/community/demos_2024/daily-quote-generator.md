
# 1. Daily Quotes Generator
- Daily Quote Display: The app displays a new inspirational quote each day, providing motivation and positivity to users. The quote updates in real-time with a visually appealing clock and date display.

- Quote Generation and Selection: Users can generate a new quote by pressing the "Generate New Quote" button. Additionally, the app allows users to request specific categories of quotes, like Motivational, Inspirational, Positive, Life, or Wisdom. Based on the input category, the app retrieves an appropriate quote using predefined logic.



## 2. Name of Issue/Topic

⭐ 07 - Daily Quote App (Hacktoberfest Demo) #608


## 3. Technologies Being Used

List the technologies and frameworks you used (e.g., CopilotKit, Next.js)
- [Next.js](https://nextjs.org)
- [Tailwind CSS](https://tailwindcss.com)
- [CopilotKit](https://copilotkit.ai)
- [ShadCN](https://ui.shadcn.com)
- Shadcn-UI Component Styling
- CopilotKit

## 4. GitHub Project Link

Click here - [Link](https://github.com/<PERSON><PERSON><PERSON><PERSON>-<PERSON>kar/Quotes-Generator) 

### 5 Live Link

- [Live app](https://quotes-generator-pi-one.vercel.app/)
 
## 6. Screenshot

![image](https://github.com/user-attachments/assets/65f7e2ae-58bc-471a-9332-f75bae71ac88)
![image](https://github.com/user-attachments/assets/efdd01f3-e76b-4065-a9c3-91889a7e1a0e)


## 7. Who am i?

Please list your **GitHub** and **Linkedin** handles if you feel comfortable doing so. 

- [Vaishnavi on github](https://github.com/Vaishnavi-Raykar)
- [Vaishnavi on linkedin](https://www.linkedin.com/in/vaishnavi-raykar-554827265/)

## 8. Posts

- A dev.to blog link - [Link](https://dev.to/vaishnavi_raykar/daily-quote-app-4bi7)

#issue 608 : Build a Quotes Generator using CopilotKit's AI interaction capabilities.
