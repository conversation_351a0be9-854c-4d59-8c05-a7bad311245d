# Welcome to CopilotKit 🪁

## 1. Project Title 
(Rename this Markdown file with the name of your project and remove line #1)

## 2. Use Case 
(What problem does this solve?)

## 3. Technologies Used 
(List the technologies and frameworks you used (e.g., CopilotKit, Next.js))

**Replace this text with your technologies**
- Next.js
- CopilotKit
- Shadcn-UI Component Styling
- MongoDB Vector Database


## 4. GitHub + YouTube

- [ ] GitHub Repo:
https://github.com/NathanTarbert/my-cool-project

- [ ] Deployed Demo:
https://github.com/NathanTarbert/my-even-cooler-deployed-project

- [ ] YouTube: 
https://my-cooler-youtube-vid

Note: Include a screenshot of your demo in action
![image](https://github.com/user-attachments/assets/5d2a020c-dc8f-4b27-85db-ba1413bdc8f6)



## 6. Who Are You?

Please list your **Twitter** & **LinkedIn** profile 

## ⭐️ Project README with installation and getting started steps ⭐️👇
Place the full detailed README here:
