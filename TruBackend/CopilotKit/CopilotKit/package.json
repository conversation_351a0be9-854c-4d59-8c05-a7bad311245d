{"private": false, "scripts": {"build": "turbo build", "dev": "turbo dev --concurrency 14", "lint": "turbo lint", "clean": "turbo clean", "test": "turbo test", "docs": "ts-node ./scripts/docs/gen.ts", "check-types": "turbo check-types", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-prettier": "prettier --check \"**/*.{ts,tsx,md}\"", "freshbuild": "pnpm -w clean && pnpm i && pnpm -w build", "precommit": "pnpm run docs", "prepublish": "turbo run build"}, "devDependencies": {"@changesets/cli": "^2.27.10", "@types/node": "^18.11.17", "eslint": "^8.56.0", "eslint-config-custom": "workspace:*", "glob": "^10.3.12", "install": "^0.13.0", "npm": "^10.7.0", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.1.11", "ts-node": "^10.9.2", "turbo": "^2.0.6", "typescript": "^5.2.3"}, "packageManager": "pnpm@9.5.0", "keywords": ["copilotkit", "copilot", "react", "nextjs", "nodejs", "ai", "assistant", "javascript", "automation", "textarea"], "name": "CopilotKit"}