{"name": "next-pages-router", "version": "1.4.6", "private": true, "scripts": {"example-dev": "next dev", "example-build": "next build", "example-start": "next start", "lint": "next lint", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist && rm -rf .next"}, "dependencies": {"@copilotkit/react-core": "workspace:*", "@copilotkit/react-textarea": "workspace:*", "@copilotkit/react-ui": "workspace:*", "@copilotkit/runtime": "workspace:*", "@copilotkit/shared": "workspace:*", "@google/generative-ai": "^0.11.2", "@heroicons/react": "^2.0.18", "@langchain/community": "^0.3.29", "@langchain/core": "^0.3.38", "@langchain/langgraph": "^0.0.12", "@langchain/openai": "^0.4.2", "clsx": "^1.2.1", "langchain": "^0.1.19", "openai": "^4.85.1", "react": "^18", "react-dom": "^18", "next": "14.2.4"}, "devDependencies": {"typescript": "^5.2.3", "@types/node": "^18.11.17", "@types/react": "^18.2.5", "@types/react-dom": "^18.2.4", "postcss": "^8.4.20", "autoprefixer": "^10.4.13", "tailwind-config": "workspace:*", "tailwindcss": "^3.2.4", "eslint": "^8", "eslint-config-next": "14.2.4", "tsconfig": "workspace:*", "eslint-config-custom": "workspace:*"}}