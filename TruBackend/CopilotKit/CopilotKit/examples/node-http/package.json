{"name": "node-http", "version": "1.4.6", "private": true, "scripts": {"example-start": "node dist/index.js", "example-dev": "nodemon ./src/index.ts"}, "dependencies": {"@copilotkit/runtime": "workspace:*", "@copilotkit/shared": "workspace:*", "openai": "^4.85.1", "dotenv": "^16.4.7"}, "devDependencies": {"@types/node": "^18.11.17", "eslint-config-custom": "workspace:*", "nodemon": "^3.1.3", "ts-node": "^10.9.2", "tsconfig": "workspace:*", "typescript": "^5.2.3"}}