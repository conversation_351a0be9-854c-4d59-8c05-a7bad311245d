{"name": "next-openai", "version": "1.4.6", "private": true, "scripts": {"example-dev": "next dev", "example-build": "next build", "example-start": "next start", "lint": "next lint", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist && rm -rf .next"}, "dependencies": {"@copilotkit/react-core": "workspace:*", "@copilotkit/react-textarea": "workspace:*", "@copilotkit/react-ui": "workspace:*", "@copilotkit/runtime": "workspace:*", "@copilotkit/shared": "workspace:*", "@copilotkit/runtime-client-gql": "workspace:*", "@google/generative-ai": "^0.11.5", "@heroicons/react": "^2.0.18", "@langchain/anthropic": "^0.3.5", "@langchain/community": "^0.3.29", "@langchain/core": "^0.3.38", "@langchain/google-gauth": "^0.1.0", "@langchain/openai": "^0.4.2", "clsx": "^1.2.1", "encoding": "^0.1.13", "eslint-config-next": "^15.0.2", "groq-sdk": "^0.5.0", "langchain": "^0.3.3", "next": "^15.0.2", "next-themes": "^0.2.1", "openai": "^4.85.1", "react": "19.0.0-rc-0bc30748-20241028", "react-dom": "19.0.0-rc-0bc30748-20241028", "react-markdown": "^8.0.7"}, "devDependencies": {"@types/node": "^18.11.17", "@types/react": "^18.2.5", "@types/react-dom": "^18.2.4", "autoprefixer": "^10.4.13", "cross-env": "^7.0.3", "eslint-config-custom": "workspace:*", "postcss": "^8.4.20", "tailwind-config": "workspace:*", "tailwindcss": "^3.2.4", "tsconfig": "workspace:*", "typescript": "^5.2.3"}}