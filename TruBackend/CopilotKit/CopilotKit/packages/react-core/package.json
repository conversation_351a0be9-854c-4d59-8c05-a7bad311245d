{"name": "@copilotkit/react-core", "private": false, "homepage": "https://github.com/CopilotKit/CopilotKit", "repository": {"type": "git", "url": "https://github.com/CopilotKit/CopilotKit.git"}, "publishConfig": {"access": "public"}, "version": "1.8.14-next.0", "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "types": "./dist/index.d.ts", "license": "MIT", "scripts": {"build": "tsup --clean", "dev": "tsup --watch", "test": "jest", "check-types": "tsc --noEmit", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist && rm -rf .next", "link:global": "pnpm link --global", "unlink:global": "pnpm unlink --global"}, "peerDependencies": {"react": "^18 || ^19 || ^19.0.0-rc", "react-dom": "^18 || ^19 || ^19.0.0-rc"}, "devDependencies": {"@types/jest": "^29.5.4", "@types/react": "^18.2.5", "@types/react-dom": "^18.2.4", "eslint": "^8.56.0", "eslint-config-custom": "workspace:*", "jest": "^29.6.4", "react": "^18.2.0", "ts-jest": "^29.1.1", "tsconfig": "workspace:*", "tsup": "^6.7.0", "typescript": "^5.2.3"}, "dependencies": {"@copilotkit/runtime-client-gql": "workspace:*", "@copilotkit/shared": "workspace:*", "@scarf/scarf": "^1.3.0", "react-markdown": "^8.0.7", "untruncate-json": "^0.0.1"}, "keywords": ["copilotkit", "copilot", "react", "nextjs", "nodejs", "ai", "assistant", "javascript", "automation", "textarea"]}