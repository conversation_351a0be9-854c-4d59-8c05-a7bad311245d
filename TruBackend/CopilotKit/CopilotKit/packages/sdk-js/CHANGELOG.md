# @copilotkit/sdk-js

## 1.8.14-next.0

### <PERSON> Changes

- @copilotkit/shared@1.8.14-next.0

## 1.8.13

### Patch Changes

- @copilotkit/shared@1.8.13

## 1.8.13-next.3

### Patch Changes

- @copilotkit/shared@1.8.13-next.3

## 1.8.13-next.2

### Patch Changes

- @copilotkit/shared@1.8.13-next.2

## 1.8.13-next.1

### Patch Changes

- @copilotkit/shared@1.8.13-next.1

## 1.8.13-next.0

### Patch Changes

- @copilotkit/shared@1.8.13-next.0

## 1.8.12

### Patch Changes

- @copilotkit/shared@1.8.12

## 1.8.12-next.6

### Patch Changes

- @copilotkit/shared@1.8.12-next.6

## 1.8.12-next.5

### Patch Changes

- @copilotkit/shared@1.8.12-next.5

## 1.8.12-next.4

### Patch Changes

- @copilotkit/shared@1.8.12-next.4

## 1.8.12-next.3

### Patch Changes

- @copilotkit/shared@1.8.12-next.3

## 1.8.12-next.2

### Patch Changes

- @copilotkit/shared@1.8.12-next.2

## 1.8.12-next.1

### Patch Changes

- @copilotkit/shared@1.8.12-next.1

## 1.8.12-next.0

### Patch Changes

- @copilotkit/shared@1.8.12-next.0

## 1.8.11

### Patch Changes

- @copilotkit/shared@1.8.11

## 1.8.11-next.1

### Patch Changes

- @copilotkit/shared@1.8.11-next.1

## 1.8.11-next.0

### Patch Changes

- @copilotkit/shared@1.8.11-next.0

## 1.8.10

### Patch Changes

- @copilotkit/shared@1.8.10

## 1.8.10-next.3

### Patch Changes

- @copilotkit/shared@1.8.10-next.3

## 1.8.10-next.2

### Patch Changes

- @copilotkit/shared@1.8.10-next.2

## 1.8.10-next.1

### Patch Changes

- @copilotkit/shared@1.8.10-next.1

## 1.8.10-next.0

### Patch Changes

- @copilotkit/shared@1.8.10-next.0

## 1.8.9

### Patch Changes

- @copilotkit/shared@1.8.9

## 1.8.9-next.0

### Patch Changes

- @copilotkit/shared@1.8.9-next.0

## 1.8.8

### Patch Changes

- @copilotkit/shared@1.8.8

## 1.8.8-next.1

### Patch Changes

- @copilotkit/shared@1.8.8-next.1

## 1.8.8-next.0

### Patch Changes

- @copilotkit/shared@1.8.8-next.0

## 1.8.7

### Patch Changes

- @copilotkit/shared@1.8.7

## 1.8.7-next.0

### Patch Changes

- @copilotkit/shared@1.8.7-next.0

## 1.8.6

### Patch Changes

- 7a04bd1: - fix: fix how results are communicated back on interrupt
  - fix: do not allow followup for interrupt actions
  - chore: improve TS docs for interrupt
  - @copilotkit/shared@1.8.6

## 1.8.6-next.0

### Patch Changes

- 7a04bd1: - fix: fix how results are communicated back on interrupt
  - fix: do not allow followup for interrupt actions
  - chore: improve TS docs for interrupt
  - @copilotkit/shared@1.8.6-next.0

## 1.8.5

### Patch Changes

- @copilotkit/shared@1.8.5

## 1.8.5-next.5

### Patch Changes

- @copilotkit/shared@1.8.5-next.5

## 1.8.5-next.4

### Patch Changes

- @copilotkit/shared@1.8.5-next.4

## 1.8.5-next.3

### Patch Changes

- @copilotkit/shared@1.8.5-next.3

## 1.8.5-next.2

### Patch Changes

- @copilotkit/shared@1.8.5-next.2

## 1.8.5-next.1

### Patch Changes

- @copilotkit/shared@1.8.5-next.1

## 1.8.5-next.0

### Patch Changes

- @copilotkit/shared@1.8.5-next.0

## 1.8.4

### Patch Changes

- Updated dependencies [f363760]
  - @copilotkit/shared@1.8.4

## 1.8.4-next.4

### Patch Changes

- @copilotkit/shared@1.8.4-next.4

## 1.8.4-next.3

### Patch Changes

- @copilotkit/shared@1.8.4-next.3

## 1.8.4-next.2

### Patch Changes

- @copilotkit/shared@1.8.4-next.2

## 1.8.4-next.1

### Patch Changes

- Updated dependencies [f363760]
  - @copilotkit/shared@1.8.4-next.1

## 1.8.4-next.0

### Patch Changes

- @copilotkit/shared@1.8.4-next.0

## 1.8.3

### Patch Changes

- @copilotkit/shared@1.8.3

## 1.8.3-next.0

### Patch Changes

- @copilotkit/shared@1.8.3-next.0

## 1.8.2-next.3

### Patch Changes

- @copilotkit/shared@1.8.2-next.3

## 1.8.2-next.2

### Patch Changes

- @copilotkit/shared@1.8.2-next.2

## 1.8.2-next.1

### Patch Changes

- @copilotkit/shared@1.8.2-next.1

## 1.8.2-next.0

### Patch Changes

- @copilotkit/shared@1.8.2-next.0

## 1.8.1

### Patch Changes

- @copilotkit/shared@1.8.1

## 1.8.1-next.1

### Patch Changes

- @copilotkit/shared@1.8.1-next.1

## 1.8.1-next.0

### Patch Changes

- @copilotkit/shared@1.8.1-next.0

## 1.8.0

### Patch Changes

- f31b093: - fix: add types for js sdk export
  - @copilotkit/shared@1.8.0

## 1.8.0-next.8

### Patch Changes

- @copilotkit/shared@1.8.0-next.8

## 1.8.0-next.7

### Patch Changes

- @copilotkit/shared@1.8.0-next.7

## 1.8.0-next.6

### Patch Changes

- f31b093: - fix: add types for js sdk export
  - @copilotkit/shared@1.8.0-next.6

## 1.8.0-next.5

### Patch Changes

- @copilotkit/shared@1.8.0-next.5

## 1.8.0-next.4

### Patch Changes

- @copilotkit/shared@1.8.0-next.4

## 1.8.0-next.3

### Patch Changes

- @copilotkit/shared@1.8.0-next.3

## 1.7.2-next.2

### Patch Changes

- @copilotkit/shared@1.7.2-next.2

## 1.7.2-next.1

### Patch Changes

- @copilotkit/shared@1.7.2-next.1

## 1.7.2-next.0

### Patch Changes

- @copilotkit/shared@1.7.2-next.0

## 1.7.1

### Patch Changes

- @copilotkit/shared@1.7.1

## 1.7.1-next.0

### Patch Changes

- @copilotkit/shared@1.7.1-next.0

## 1.7.0

### Patch Changes

- @copilotkit/shared@1.7.0

## 1.7.0-next.1

### Patch Changes

- @copilotkit/shared@1.7.0-next.1

## 1.7.0-next.0

### Patch Changes

- @copilotkit/shared@1.7.0-next.0

## 1.6.0

### Patch Changes

- Updated dependencies [090203d]
  - @copilotkit/shared@1.6.0

## 1.6.0-next.12

### Patch Changes

- @copilotkit/shared@1.6.0-next.12

## 1.6.0-next.11

### Patch Changes

- @copilotkit/shared@1.6.0-next.11

## 1.6.0-next.10

### Patch Changes

- @copilotkit/shared@1.6.0-next.10

## 1.6.0-next.9

### Patch Changes

- @copilotkit/shared@1.6.0-next.9

## 1.6.0-next.8

### Patch Changes

- @copilotkit/shared@1.6.0-next.8

## 1.6.0-next.7

### Patch Changes

- @copilotkit/shared@1.6.0-next.7

## 1.6.0-next.6

### Patch Changes

- @copilotkit/shared@1.6.0-next.6

## 1.6.0-next.5

### Patch Changes

- Updated dependencies [090203d]
  - @copilotkit/shared@1.6.0-next.5

## 1.6.0-next.4

### Patch Changes

- @copilotkit/shared@1.6.0-next.4

## 1.6.0-next.3

### Patch Changes

- @copilotkit/shared@1.6.0-next.3

## 1.6.0-next.2

### Patch Changes

- @copilotkit/shared@1.6.0-next.2

## 1.6.0-next.1

### Patch Changes

- @copilotkit/shared@1.6.0-next.1

## 1.6.0-next.0

### Patch Changes

- @copilotkit/shared@1.6.0-next.0

## 1.5.20

### Patch Changes

- Updated dependencies [51f0d66]
  - @copilotkit/shared@1.5.20

## 1.5.20-next.0

### Patch Changes

- Updated dependencies [51f0d66]
  - @copilotkit/shared@1.5.20-next.0

## 1.5.19

### Patch Changes

- Updated dependencies [0dd1ab9]
  - @copilotkit/shared@1.5.19

## 1.5.19-next.1

### Patch Changes

- Updated dependencies [0dd1ab9]
  - @copilotkit/shared@1.5.19-next.1

## 1.5.19-next.0

### Patch Changes

- @copilotkit/shared@1.5.19-next.0

## 1.5.18

### Patch Changes

- Updated dependencies [d47cd26]
- Updated dependencies [f77a7b9]
- Updated dependencies [38d3ac2]
  - @copilotkit/shared@1.5.18

## 1.5.18-next.3

### Patch Changes

- Updated dependencies [f77a7b9]
  - @copilotkit/shared@1.5.18-next.3

## 1.5.18-next.2

### Patch Changes

- Updated dependencies [38d3ac2]
  - @copilotkit/shared@1.5.18-next.2

## 1.5.18-next.1

### Patch Changes

- @copilotkit/shared@1.5.18-next.1

## 1.5.18-next.0

### Patch Changes

- Updated dependencies [d47cd26]
  - @copilotkit/shared@1.5.18-next.0

## 1.5.17

### Patch Changes

- Updated dependencies [1fc3902]
  - @copilotkit/shared@1.5.17

## 1.5.17-next.0

### Patch Changes

- Updated dependencies [1fc3902]
  - @copilotkit/shared@1.5.17-next.0

## 1.5.16

### Patch Changes

- Updated dependencies [48b7c7b]
  - @copilotkit/shared@1.5.16

## 1.5.16-next.2

### Patch Changes

- @copilotkit/shared@1.5.16-next.2

## 1.5.16-next.1

### Patch Changes

- Updated dependencies [48b7c7b]
  - @copilotkit/shared@1.5.16-next.1

## 1.5.16-next.0

### Patch Changes

- @copilotkit/shared@1.5.16-next.0

## 1.5.15

### Patch Changes

- 06f9f35: - feat(interrupt): add copilotkit interrupt as messages with copilotkit interrupt convenience fn
  - chore(deps): update dependencies for demos
  - chore(interrupt-as-message): add e2e test for interrupt as message
- Updated dependencies [7b3141d]
  - @copilotkit/shared@1.5.15

## 1.5.15-next.8

### Patch Changes

- 06f9f35: - feat(interrupt): add copilotkit interrupt as messages with copilotkit interrupt convenience fn
  - chore(deps): update dependencies for demos
  - chore(interrupt-as-message): add e2e test for interrupt as message
  - @copilotkit/shared@1.5.15-next.8

## 1.5.15-next.7

### Patch Changes

- @copilotkit/shared@1.5.15-next.7

## 1.5.15-next.6

### Patch Changes

- @copilotkit/shared@1.5.15-next.6

## 1.5.15-next.5

### Patch Changes

- @copilotkit/shared@1.5.15-next.5

## 1.5.15-next.4

### Patch Changes

- Updated dependencies [7b3141d]
  - @copilotkit/shared@1.5.15-next.4

## 1.5.15-next.3

### Patch Changes

- @copilotkit/shared@1.5.15-next.3

## 1.5.15-next.2

### Patch Changes

- @copilotkit/shared@1.5.15-next.2

## 1.5.15-next.1

### Patch Changes

- @copilotkit/shared@1.5.15-next.1

## 1.5.15-next.0

### Patch Changes

- @copilotkit/shared@1.5.15-next.0

## 1.5.14

### Patch Changes

- Updated dependencies [0061f65]
  - @copilotkit/shared@1.5.14

## 1.5.14-next.0

### Patch Changes

- Updated dependencies [0061f65]
  - @copilotkit/shared@1.5.14-next.0

## 1.5.13

### Patch Changes

- @copilotkit/shared@1.5.13

## 1.5.13-next.0

### Patch Changes

- @copilotkit/shared@1.5.13-next.0

## 1.5.12

### Patch Changes

- Updated dependencies [6136a57]
  - @copilotkit/shared@1.5.12

## 1.5.12-next.7

### Patch Changes

- @copilotkit/shared@1.5.12-next.7

## 1.5.12-next.6

### Patch Changes

- Updated dependencies [6136a57]
  - @copilotkit/shared@1.5.12-next.6

## 1.5.12-next.5

### Patch Changes

- @copilotkit/shared@1.5.12-next.5

## 1.5.12-next.4

### Patch Changes

- @copilotkit/shared@1.5.12-next.4

## 1.5.12-next.3

### Patch Changes

- @copilotkit/shared@1.5.12-next.3

## 1.5.12-next.2

### Patch Changes

- @copilotkit/shared@1.5.12-next.2

## 1.5.12-next.1

### Patch Changes

- @copilotkit/shared@1.5.12-next.1

## 1.5.12-next.0

### Patch Changes

- @copilotkit/shared@1.5.12-next.0

## 1.5.11

### Patch Changes

- @copilotkit/shared@1.5.11

## 1.5.11-next.0

### Patch Changes

- @copilotkit/shared@1.5.11-next.0

## 1.5.10

### Patch Changes

- @copilotkit/shared@1.5.10

## 1.5.10-next.0

### Patch Changes

- @copilotkit/shared@1.5.10-next.0

## 1.5.9

### Patch Changes

- @copilotkit/shared@1.5.9

## 1.5.8

### Patch Changes

- @copilotkit/shared@1.5.8

## 1.5.6-next.0

### Patch Changes

- @copilotkit/shared@1.5.6-next.0

## 1.5.5-next.5

### Patch Changes

- @copilotkit/shared@1.5.5-next.5

## 1.5.5-next.3

### Patch Changes

- @copilotkit/shared@1.5.5-next.3

## 1.5.5-next.2

### Patch Changes

- @copilotkit/shared@1.5.5-next.2

## 1.5.4

### Patch Changes

- @copilotkit/shared@1.5.4

## 1.5.3

### Patch Changes

- @copilotkit/shared@1.5.3

## 1.5.2

### Patch Changes

- @copilotkit/shared@1.5.2

## 1.5.1

### Patch Changes

- 5c01e9e: test prerelease #4
- da280ed: Test prerelease script
- 27e42d7: testing a prerelease
- 05240a9: test pre #2
- 33218fe: test prerelease #3
- 03f3d6f: Test next prerelease
- Updated dependencies [5c01e9e]
- Updated dependencies [da280ed]
- Updated dependencies [27e42d7]
- Updated dependencies [05240a9]
- Updated dependencies [33218fe]
- Updated dependencies [03f3d6f]
  - @copilotkit/shared@1.5.1

## 1.5.1-next.3

### Patch Changes

- 33218fe: test prerelease #3
- Updated dependencies [33218fe]
  - @copilotkit/shared@1.5.1-next.3

## 1.5.1-next.2

### Patch Changes

- da280ed: Test prerelease script
- Updated dependencies [da280ed]
  - @copilotkit/shared@1.5.1-next.2

## 1.5.1-next.1

### Patch Changes

- 03f3d6f: Test next prerelease
- Updated dependencies [03f3d6f]
  - @copilotkit/shared@1.5.1-next.1

## 1.5.1-next.0

### Patch Changes

- 27e42d7: testing a prerelease
- Updated dependencies [27e42d7]
  - @copilotkit/shared@1.5.1-next.0

## 1.5.0

### Minor Changes

- 1b47092: Synchronize LangGraph messages with CopilotKit

### Patch Changes

- 1b47092: CoAgents v0.3 prerelease
- Updated dependencies [1b47092]
- Updated dependencies [1b47092]
  - @copilotkit/shared@1.5.0

## 1.5.0-coagents-v0-3.0

### Minor Changes

- Synchronize LangGraph messages with CopilotKit

### Patch Changes

- e66bce4: CoAgents v0.3 prerelease
- Updated dependencies
- Updated dependencies [e66bce4]
  - @copilotkit/shared@1.5.0-coagents-v0-3.0

## 1.4.8

### Patch Changes

- - Better error handling
  - Introduce new "EmptyLLMAdapter" for when using CoAgents
  - Improve dev console help options
  - Allow CopilotKit remote endpoint without agents
- Updated dependencies
  - @copilotkit/shared@1.4.8

## 1.4.8-next.0

### Patch Changes

- @copilotkit/shared@1.4.8-next.0

## 1.4.7

### Patch Changes

- Fix broken build script before release
- Updated dependencies
  - @copilotkit/shared@1.4.7

## 1.4.6

### Patch Changes

- .

## 1.4.5

### Patch Changes

- testing release workflow
- Updated dependencies
  - @copilotkit/shared@1.4.5

## 1.4.5-next.0

### Patch Changes

- testing release workflow
- Updated dependencies
  - @copilotkit/shared@1.4.5-next.0

## 1.4.4

### Patch Changes

- @copilotkit/shared@1.4.4

## 1.4.4-next.4

### Patch Changes

- @copilotkit/shared@1.4.4-next.4

## 1.4.4-next.3

### Patch Changes

- @copilotkit/shared@1.4.4-next.3

## 1.4.4-next.2

### Patch Changes

- @copilotkit/shared@1.4.4-next.2

## 1.4.4-next.1

### Patch Changes

- @copilotkit/shared@1.4.4-next.1

## 1.4.4-next.0

### Patch Changes

- @copilotkit/shared@1.4.4-next.0

## 1.4.3

### Patch Changes

- c296282: - Better error surfacing when using LangGraph Platform streaming
  - Ensure state is immediately set without using flushSync
- - Better error surfacing when using LangGraph Platform streaming
  - Ensure state is immediately set without using flushSync
- Updated dependencies [c296282]
- Updated dependencies
  - @copilotkit/shared@1.4.3

## 1.4.3-pre.0

### Patch Changes

- - Better error surfacing when using LangGraph Platform streaming
  - Ensure state is immediately set without using flushSync
- Updated dependencies
  - @copilotkit/shared@1.4.3-pre.0

## 1.4.2

### Patch Changes

- - Make sure agent state is set immediately (#1077)
  - Support running an agent without messages (#1075)
- Updated dependencies
  - @copilotkit/shared@1.4.2

## 1.4.1

### Patch Changes

- 1721cbd: lower case copilotkit property
- 1721cbd: add zod conversion
- 8d0144f: bump
- 8d0144f: bump
- 8d0144f: bump
- e16d95e: New prerelease
- 1721cbd: Add convertActionsToDynamicStructuredTools to sdk-js
- CopilotKit Core:

  - Improved error messages and overall logs
  - `useCopilotAction.renderAndAwait` renamed to `.renderAndAwaitForResponse` (backwards compatible, will be deprecated in the future)
  - Improved scrolling behavior. It is now possible to scroll up during LLM response generation
  - Added Azure OpenAI integration
  - Updated interfaces for better developer ergonomics

  CoAgents:

  - Renamed `remoteActions` to `remoteEndpoints` (backwards compatible, will be deprecated in the future)
  - Support for LangGraph Platform in Remote Endpoints
  - LangGraph JS Support for CoAgents (locally via `langgraph dev`, `langgraph up` or deployed to LangGraph Platform)
  - Improved LangSmith integration - requests made through CoAgents will now surface in LangSmith
  - Enhanced state management and message handling

  CopilotKid Back-end SDK:

  - Released a whole-new `@copilotkit/sdk-js` for building agents with LangGraph JS Support

- 8d0144f: bump
- 8d0144f: bump
- fef1b74: fix assistant message CSS and propagate actions to LG JS
- Updated dependencies [1721cbd]
- Updated dependencies [1721cbd]
- Updated dependencies [8d0144f]
- Updated dependencies [8d0144f]
- Updated dependencies [8d0144f]
- Updated dependencies [e16d95e]
- Updated dependencies [1721cbd]
- Updated dependencies
- Updated dependencies [8d0144f]
- Updated dependencies [8d0144f]
- Updated dependencies [fef1b74]
  - @copilotkit/shared@1.4.1

## 1.4.1-pre.6

### Patch Changes

- 1721cbd: lower case copilotkit property
- 1721cbd: add zod conversion
- 1721cbd: Add convertActionsToDynamicStructuredTools to sdk-js
- fix assistant message CSS and propagate actions to LG JS
- Updated dependencies [1721cbd]
- Updated dependencies [1721cbd]
- Updated dependencies [1721cbd]
- Updated dependencies
  - @copilotkit/shared@1.4.1-pre.6

## 1.4.1-pre.5

### Patch Changes

- bump
- Updated dependencies
  - @copilotkit/shared@1.4.1-pre.5

## 1.4.1-pre.4

### Patch Changes

- bump
- Updated dependencies
  - @copilotkit/shared@1.4.1-pre.4

## 1.4.1-pre.3

### Patch Changes

- bump
- Updated dependencies
  - @copilotkit/shared@1.4.1-pre.3

## 1.4.1-pre.2

### Patch Changes

- bump
- Updated dependencies
  - @copilotkit/shared@1.4.1-pre.2

## 1.4.1-pre.1

### Patch Changes

- bump
- Updated dependencies
  - @copilotkit/shared@1.4.1-pre.1

## 1.4.1-pre.0

### Patch Changes

- New prerelease
- Updated dependencies
  - @copilotkit/shared@1.4.1-pre.0

## 1.4.0

### Minor Changes

CopilotKit Core:

- Improved error messages and overall logs
- `useCopilotAction.renderAndAwait` renamed to `.renderAndAwaitForResponse` (backwards compatible, will be deprecated in the future)
- Improved scrolling behavior. It is now possible to scroll up during LLM response generation
- Added Azure OpenAI integration
- Updated interfaces for better developer ergonomics

CoAgents:

- Renamed `remoteActions` to `remoteEndpoints` (backwards compatible, will be deprecated in the future)
- Support for LangGraph Platform in Remote Endpoints
- LangGraph JS Support for CoAgents (locally via `langgraph dev`, `langgraph up` or deployed to LangGraph Platform)
- Improved LangSmith integration - requests made through CoAgents will now surface in LangSmith
- Enhanced state management and message handling

CopilotKid Back-end SDK:

- Released a whole-new `@copilotkit/sdk-js` for building agents with LangGraph JS Support

### Patch Changes

- f6fab28: update tsup config
- f6fab28: update entry
- f6fab28: export langchain module
- f6fab28: Ensure intermediate state config is sent as snake case
- f6fab28: update entry in tsup config
- a5efccd: Revert rxjs changes
- f6fab28: update entry
- f6fab28: Update exports
- f6fab28: Update exports
- f6fab28: Export LangGraph functions
- f6fab28: Update lockfile
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies
- Updated dependencies [f6fab28]
- Updated dependencies [8a77944]
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies [8a77944]
- Updated dependencies [a5efccd]
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies [332d744]
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
  - @copilotkit/shared@1.4.0

## 1.3.16-mme-revert-rxjs-changes.10

### Patch Changes

- f6fab28: update tsup config
- f6fab28: update entry
- f6fab28: export langchain module
- f6fab28: Ensure intermediate state config is sent as snake case
- f6fab28: update entry in tsup config
- Revert rxjs changes
- f6fab28: update entry
- f6fab28: Update exports
- f6fab28: Update exports
- f6fab28: Export LangGraph functions
- f6fab28: Update lockfile
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies [8a77944]
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies [8a77944]
- Updated dependencies
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies [332d744]
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
  - @copilotkit/shared@1.3.16-mme-revert-rxjs-changes.10

## 1.3.16-mme-lgc-langgraph-package.9

### Patch Changes

- update entry
- Updated dependencies
  - @copilotkit/shared@1.3.16-mme-lgc-langgraph-package.9

## 1.3.16-mme-lgc-langgraph-package.8

### Patch Changes

- update entry
- Updated dependencies
  - @copilotkit/shared@1.3.16-mme-lgc-langgraph-package.8

## 1.3.16-mme-lgc-langgraph-package.7

### Patch Changes

- update entry in tsup config
- Updated dependencies
  - @copilotkit/shared@1.3.16-mme-lgc-langgraph-package.7

## 1.3.16-mme-lgc-langgraph-package.6

### Patch Changes

- Update exports
- Updated dependencies
  - @copilotkit/shared@1.3.16-mme-lgc-langgraph-package.6

## 1.3.16-mme-lgc-langgraph-package.5

### Patch Changes

- update tsup config
- Updated dependencies
  - @copilotkit/shared@1.3.16-mme-lgc-langgraph-package.5

## 1.3.16-mme-lgc-langgraph-package.4

### Patch Changes

- Update exports
- Updated dependencies
  - @copilotkit/shared@1.3.16-mme-lgc-langgraph-package.4

## 1.3.16-mme-lgc-langgraph-package.3

### Patch Changes

- export langchain module
- Updated dependencies
  - @copilotkit/shared@1.3.16-mme-lgc-langgraph-package.3

## 1.3.16-mme-sdk-js.2

### Patch Changes

- Ensure intermediate state config is sent as snake case
- Updated dependencies
  - @copilotkit/shared@1.3.16-mme-sdk-js.2

## 1.3.16-mme-sdk-js.1

### Patch Changes

- Update lockfile
- Updated dependencies
  - @copilotkit/shared@1.3.16-mme-sdk-js.1

## 1.3.16-mme-sdk-js.0

### Patch Changes

- Export LangGraph functions
- Updated dependencies
  - @copilotkit/shared@1.3.16-mme-sdk-js.0
