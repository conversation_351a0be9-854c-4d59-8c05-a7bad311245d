.copilotKitHeader {
  height: 56px;
  font-weight: 500;
  display: flex;
  align-items: center;
  position: relative;
  color: var(--copilot-kit-primary-color);
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom: 1px solid var(--copilot-kit-separator-color);
  padding-left: 1.5rem;
  background-color: var(--copilot-kit-contrast-color);
  justify-content: space-between;
  z-index: 2;
}

.copilotKitSidebar .copilotKitHeader {
  border-radius: 0;
}

.copilotKitHeaderControls {
  display: flex;
}

.copilotKitHeaderCloseButton {
  background: none;
  border: none;
}

@media (min-width: 640px) {
  .copilotKitHeader {
    padding-left: 1.5rem;
    padding-right: 24px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }
}

.copilotKitHeader > button {
  border: 0;
  padding: 8px;
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  outline: none;
  color: var(--copilot-kit-muted-color);
  background-color: transparent;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  width: 35px;
  height: 35px;
}

.copilotKitHeader > button:hover {
  color: color-mix(in srgb, var(--copilot-kit-muted-color) 80%, black);
}

.copilotKitHeader > button:focus {
  outline: none;
}
