/* IMPORTANT NOTE:
THE DARK AND LIGHT COLORS HERE ARE DUPLICATED BECAUSE NO REUSE METHOD POSSIBLE.
WHEN MAKING ANY CHANGE, MAKE SURE TO INCLUDE IT IN ALL DUPLICATIONS.
*/

/* BASE LIGHT THEME */
:root {
  /* Semantic color tokens */
  /* Main brand/action color - used for buttons, interactive elements */
  --copilot-kit-primary-color: rgb(28, 28, 28);
  /* Color that contrasts with primary - used for text on primary elements */
  --copilot-kit-contrast-color: rgb(255, 255, 255);
  /* Main page/container background color */
  --copilot-kit-background-color: rgb(255 255 255);
  /* Input box background color */
  --copilot-kit-input-background-color: #fbfbfb;
  /* Secondary background - used for cards, panels, elevated surfaces */
  --copilot-kit-secondary-color: rgb(255 255 255);
  /* Primary text color for main content */
  --copilot-kit-secondary-contrast-color: rgb(28, 28, 28);
  /* Border color for dividers and containers */
  --copilot-kit-separator-color: rgb(200 200 200);
  /* Muted color for disabled/inactive states */
  --copilot-kit-muted-color: rgb(200 200 200);

  /* Shadow tokens */
  /* Small shadow for subtle elevation */
  --copilot-kit-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  /* Medium shadow for cards */
  --copilot-kit-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  /* Large shadow for modals */
  --copilot-kit-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  --copilot-kit-dev-console-bg: #f8f8fa;
  --copilot-kit-dev-console-text: black;
}

/* BASE DARK THEME */
.dark,
html.dark,
body.dark,
[data-theme="dark"],
html[style*="color-scheme: dark"],
body[style*="color-scheme: dark"] :root {
  /* Main brand/action color - used for buttons, interactive elements */
  --copilot-kit-primary-color: rgb(255, 255, 255);
  /* Color that contrasts with primary - used for text on primary elements */
  --copilot-kit-contrast-color: rgb(28, 28, 28);
  /* Main page/container background color */
  --copilot-kit-background-color: rgb(17, 17, 17);
  /* Input box background color */
  --copilot-kit-input-background-color: #2c2c2c;
  /* Secondary background - used for cards, panels, elevated surfaces */
  --copilot-kit-secondary-color: rgb(28, 28, 28);
  /* Primary text color for main content */
  --copilot-kit-secondary-contrast-color: rgb(255, 255, 255);
  /* Border color for dividers and containers */
  --copilot-kit-separator-color: rgb(45, 45, 45);
  /* Muted color for disabled/inactive states */
  --copilot-kit-muted-color: rgb(45, 45, 45);

  /* Small shadow for subtle elevation */
  --copilot-kit-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  /* Medium shadow for cards */
  --copilot-kit-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  /* Large shadow for modals */
  --copilot-kit-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}
