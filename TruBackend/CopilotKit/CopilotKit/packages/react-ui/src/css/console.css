.copilotKitDevConsole {
  display: flex;
  align-items: center;
  gap: 5px;
  margin: 0 15px;
}

.copilotKitDevConsole.copilotKitDevConsoleWarnOutdated {
  background-color: var(--copilot-kit-dev-console-bg);
}

.copilotKitDevConsole .copilotKitVersionInfo {
  display: flex;
  position: absolute;
  bottom: -25px;
  padding: 3px 5px;
  left: 0;
  width: 100%;
  justify-content: center;
  gap: 10px;
  font-size: 0.8rem;
  align-items: center;
  background: #ebb305;
}

.copilotKitDevConsole .copilotKitVersionInfo button {
  font-size: 11px;
  font-weight: normal;
  font-family: monospace;
  background-color: var(--copilot-kit-dev-console-bg);
  border: 1px solid #979797;
  padding: 1px 12px;
  padding-left: 5px;
  border-radius: 4px;
  display: inline-block;
  text-align: left;
  overflow: hidden;
  white-space: nowrap;
  width: 260px;
  text-overflow: ellipsis;
}

.copilotKitDevConsole .copilotKitVersionInfo aside {
  display: inline;
  font-weight: normal;
  color: #7f7a7a;
  margin-left: 5px;
}

.copilotKitDevConsole .copilotKitVersionInfo svg {
  margin-left: 3px;
  margin-top: -3px;
}

.copilotKitDevConsole .copilotKitDebugMenuTriggerButton {
  font-size: 11px;
  font-weight: bold;
  display: flex;
  padding: 0 10px;
  height: 30px;
  background-color: transparent;
  border: 1px solid var(--copilot-kit-muted-color);
  border-radius: 20px;
  align-items: center;
  justify-content: center;
  outline: none;
}

.copilotKitDebugMenuTriggerButton.compact {
  width: 35px;
  color: var(--copilot-kit-dev-console-bg);
  justify-content: center;
  outline: none;
  font-size: 8px;
}

.copilotKitDevConsole .copilotKitDebugMenuTriggerButton:hover {
  background-color: color-mix(in srgb, var(--copilot-kit-dev-console-bg) 85%, black);
  color: var(--copilot-kit-dev-console-text);
}

.dark,
html.dark,
body.dark,
[data-theme="dark"],
html[style*="color-scheme: dark"],
body[style*="color-scheme: dark"] .copilotKitDevConsole .copilotKitDebugMenuTriggerButton {
  color: white;
}

.dark,
html.dark,
body.dark,
[data-theme="dark"],
html[style*="color-scheme: dark"],
body[style*="color-scheme: dark"] .copilotKitDevConsole .copilotKitDebugMenuTriggerButton:hover {
  background-color: color-mix(in srgb, var(--copilot-kit-dev-console-bg) 20%, black);
}

.copilotKitDevConsole .copilotKitDebugMenuTriggerButton > svg {
  margin-left: 10px;
}

.copilotKitDebugMenu {
  --copilot-kit-dev-console-border: color-mix(in srgb, var(--copilot-kit-dev-console-bg) 80%, black);
  margin-top: 2px;
  border-radius: 6px;
  background-color: var(--copilot-kit-dev-console-bg);
  border: 1px solid var(--copilot-kit-dev-console-border);
  padding: 0.25rem;
  outline: none;
  font-size: 13px;
}

.copilotKitDebugMenuItem {
  padding-top: 3px;
  padding-bottom: 3px;
  padding-left: 10px;
  padding-right: 10px;
  display: block;
  width: 100%;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--copilot-kit-dev-console-text);
}

.copilotKitDebugMenuItem:hover {
  background-color: color-mix(in srgb, var(--copilot-kit-dev-console-bg) 95%, black);
  border-radius: 4px;
}

.copilotKitDebugMenu[data-closed] {
  transform: scale(0.95); /* data-[closed]:scale-95 */
  opacity: 0; /* data-[closed]:opacity-0 */
}

.copilotKitDebugMenu hr {
  height: 1px;
  border: none; /* Remove 3D look */
  background-color: var(--copilot-kit-dev-console-border);
  margin: 0.25rem;
}

.copilotKitHelpModal {
  background-color: var(--copilot-kit-dev-console-bg);
  color: var(--copilot-kit-dev-console-text);
}

.copilotKitHelpItemButton {
  display: block;
  text-align: center;
  width: 100%;
  padding: 4px 6px;
  border-radius: 15px;
  font-size: 0.8rem;
  border: 1px solid var(--copilot-kit-muted-color);
  color: var(--copilot-kit-dev-console-text);
  box-shadow: 0 5px 5px 0px rgba(0,0,0,.01),0 2px 3px 0px rgba(0,0,0,.02);
  background-color: var(--copilot-kit-dev-console-bg);
}
.copilotKitHelpItemButton:hover {
  background-color: color-mix(in srgb, var(--copilot-kit-dev-console-bg) 95%, black);
}

