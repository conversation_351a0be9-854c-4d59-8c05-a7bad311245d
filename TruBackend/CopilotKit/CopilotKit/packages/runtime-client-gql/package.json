{"name": "@copilotkit/runtime-client-gql", "private": false, "homepage": "https://github.com/CopilotKit/CopilotKit", "repository": {"type": "git", "url": "https://github.com/CopilotKit/CopilotKit.git"}, "publishConfig": {"access": "public"}, "version": "1.8.14-next.0", "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "types": "./dist/index.d.ts", "license": "MIT", "scripts": {"build": "pnpm run graphql-codegen && tsup --clean", "dev": "concurrently \"pnpm run graphql-codegen:watch\" \"tsup --watch --no-splitting\"", "test": "jest --passWithNoTests", "check-types": "tsc --noEmit", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf ./src/graphql/@generated && rm -rf dist && rm -rf .next", "graphql-codegen": "graphql-codegen -c codegen.ts", "graphql-codegen:watch": "graphql-codegen -c codegen.ts --watch", "link:global": "pnpm link --global", "unlink:global": "pnpm unlink --global"}, "peerDependencies": {"react": "^18 || ^19 || ^19.0.0-rc"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/client-preset": "^4.2.6", "@graphql-codegen/introspection": "^4.0.3", "@graphql-codegen/typescript": "^4.0.7", "@graphql-codegen/typescript-operations": "^4.2.1", "@graphql-codegen/typescript-urql": "^4.0.0", "@graphql-codegen/urql-introspection": "^3.0.0", "@graphql-typed-document-node/core": "^3.2.0", "@parcel/watcher": "^2.4.1", "@types/node": "^20.12.12", "concurrently": "^8.2.2", "esbuild": "^0.23.0", "jest": "^29.6.4", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsup": "^6.7.0", "typescript": "^5.4.5", "@copilotkit/runtime": "workspace:*", "graphql": "^16.8.1"}, "dependencies": {"@copilotkit/shared": "workspace:*", "@urql/core": "^5.0.3", "untruncate-json": "^0.0.1", "urql": "^4.1.0"}, "keywords": ["copilotkit", "copilot", "react", "nextjs", "nodejs", "ai", "assistant", "javascript", "automation", "textarea"]}