@tailwind base;
@tailwind components;
@tailwind utilities;

.line.diff.remove {
  @apply px-0 mx-0;
  background-color: rgb(255, 220, 220);
  @apply dark:bg-red-950/70;
}

.line.diff.add {
  @apply px-0 mx-0;
  background-color: rgb(220, 255, 220);
  @apply dark:bg-green-950/70;
}

@layer base {
  #nd-sidebar > div.overflow-hidden.flex-1 > div{
    @apply px-4;
  }
  #model-context-protocol-banner > button {
    @apply text-white hover:text-black dark:text-white dark:hover:bg-white dark:hover:text-black;
  }

  :root {
    --fd-sidebar-width: 330px !important;
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --success: 142 72% 29%;
    --success-foreground: 0 0% 98%;
    --success-muted: 142 76% 90%;
    --success-muted-foreground: 142 72% 29%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;
    --warning-muted: 38 92% 90%;
    --warning-muted-foreground: 38 92% 30%;
    --error: 0 84.2% 60.2%;
    --error-foreground: 0 0% 98%;
    --error-muted: 0 84.2% 90%;
    --error-muted-foreground: 0 84.2% 40%;
    --info: 214 95% 48%;
    --info-foreground: 0 0% 98%;
    --info-muted: 214 95% 90%;
    --info-muted-foreground: 214 95% 30%;
  }
  @media (max-width: 1200px) {
    :root {
      --fd-sidebar-width: 280px !important;
    }
  }
  @media (max-width: 640px) {
    :root {
      --fd-sidebar-width: 260px !important;
    }
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --success: 142 72% 46%;
    --success-foreground: 0 0% 98%;
    --success-muted: 142 76% 20%;
    --success-muted-foreground: 142 72% 80%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;
    --warning-muted: 38 92% 20%;
    --warning-muted-foreground: 38 92% 80%;
    --error: 0 62.8% 30.6%;
    --error-foreground: 0 0% 98%;
    --error-muted: 0 62.8% 20%;
    --error-muted-foreground: 0 62.8% 80%;
    --info: 214 95% 60%;
    --info-foreground: 0 0% 98%;
    --info-muted: 214 95% 20%;
    --info-muted-foreground: 214 95% 80%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  .fd-codeblock pre {
    @apply max-h-none;
  }

  div[data-toc] {
    @apply lg:mt-10;
  }

  article {
    @apply lg:mt-10;
  }
} 