---
title: Agentic Generative UI
icon: "lucide/Bot"
description: Render the state of your agent with custom UI components.
---

import InstallSDKSnippet from "@/snippets/install-python-sdk-crew.mdx";
import { Accordions, Accordion } from "fumadocs-ui/components/accordion";

<video
  src="/images/coagents/agentic-generative-ui.mp4"
  className="rounded-lg shadow-xl"
  loop
  playsInline
  controls
  autoPlay
  muted
/>
<Callout>
  This video demonstrates the [implementation](#implementation) section applied
  to out [coagents starter
  project](https://github.com/CopilotKit/CopilotKit/tree/main/examples/coagents-starter-crewai-crews).
</Callout>

## What is this?

All CrewAI Crew agents support `inputs` to the crew and `outputs`, i.e. the overall result generated by the crew. The state of the `outputs` is available to your React frontend by using the `useCoAgent` hook, which allows you to easily render the state of your agent in your application. We call this feature **Agentic Generative UI**.

## When should I use this?

Rendering the outputs of your crew in the UI is essential to provide the user with the end result of a crew run. The agent can provide the result to the user which is then rendered in the UI.

## Implementation

<Steps>
  <Step>
    ### Run and Connect your CrewAI Crew to CopilotKit
    First, you'll need to make sure you have a running CrewAI Crew. If you haven't already done this, you can follow the [getting started guide](/crewai-crews/quickstart/crewai)

    This guide uses the [CoAgents starter repo](https://github.com/CopilotKit/CopilotKit/tree/main/examples/coagents-starter-crewai-crews) as its starting point.

  </Step>
  <Step>
    ### Render state of the agent in the chat
    Now we can utilize `useCoAgentStateRender` to render the state of our agent **in the chat**.

    ```tsx title="app/page.tsx"
    // ...
    import { useCoAgent } from "@copilotkit/react-core";
    // ...

    // Define the state of the agent, should match the state of the agent.
    type AgentState = {
      inputs: {
        topic: string,
        current_year: string,
      },
      outputs: string,
    };

    function YourMainContent() {
      // ...

      // [!code highlight:14]
      // styles omitted for brevity
      const { state } = useCoAgent<AgentState>({
        name: "research_crew",
        initialState: {
          outputs: "Report will appear here",
        },
      });

      return (
        <div
            id="result"
          >
            <MarkdownRenderer content={state.outputs} />
          </div>
      )
    }
    ```

  </Step>
  <Step>
    ### Give it a try!

    You've now created a component that will render the agent's state in the chat.

  </Step>
</Steps>
