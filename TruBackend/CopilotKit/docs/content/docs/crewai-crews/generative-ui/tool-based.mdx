---
title: Tool-based Generative UI
icon: "lucide/Wrench"
description: Render your agent's tool calls with custom UI components.
---

import { Accordions, Accordion } from "fumadocs-ui/components/accordion";
import RunAndConnectAgentSnippet from "@/snippets/coagents/run-and-connect-agent.mdx";

<video
  src="/images/coagents/tool-based-gen-ui.mp4"
  className="rounded-lg shadow-xl"
  loop
  playsInline
  controls
  autoPlay
  muted
/>
<Callout>
  This video demonstrates the [implementation](#implementation) section applied
  to out [coagents starter
  project](https://github.com/CopilotKit/CopilotKit/tree/main/examples/coagents-starter-crewai-crews).
</Callout>

## What is this?

Tools are a way for the LLM to call predefined, typically, deterministic functions. CopilotKit allows you to render these tools in the UI
as a custom component, which we call **Generative UI**.

## When should I use this?

Rendering tools in the UI is useful when you want to provide the user with feedback about what your agent is doing, specifically
when your agent is calling tools. CopilotKit allows you to fully customize how these tools are rendered in the chat.

## Implementation

<Steps>
<Step>
### Run and connect your agent

You'll need to run your agent and connect it to CopilotKit before proceeding. If you haven't done so already,
you can follow the instructions in the [Getting Started](/crewai-crews/quickstart/crewai) guide.

If you don't already have an agent, you can use the [coagent starter](https://github.com/CopilotKit/CopilotKit/tree/main/examples/coagents-starter-crewai-crews) as a starting point
as this guide uses it as a starting point.

</Step>
<Step>
### Render the tool call in your frontend

In the case of CrewAI Crew agents, there is one tool call that indicates that the crew is being executed. It has the same name as the crew. In this example, the crew is named `research_crew`. To display progress of the crew, we just need to add a `useCopilotAction` hook to render the tool call in
the UI.

<Callout type="info" title="Important">
  In order to render a tool call in the UI, the name of the action must match
  the name of the tool.
</Callout>

```tsx title="app/page.tsx"
import { useCopilotAction } from "@copilotkit/react-core"; // [!code highlight]
// ...

const YourMainContent = () => {
  // ...
  // [!code highlight:21]
  useCopilotAction({
    name: "research_crew",
    parameters: [
      {
        name: "topic",
      },
      {
        name: "current_year",
      },
    ],
    render({ args, status }) {
      return (
        <div className="m-4 p-4 bg-gray-100 rounded shadow">
          <h1 className="text-center text-sm">
            Researching {args.topic} in {args.current_year}{" "}
            {status == "complete" ? "✅" : "⏳"}
          </h1>
        </div>
      );
    },
  });
  // ...
};
```

</Step>
<Step>
### Give it a try!

Try giving the crew enough information to complete the task, i.e. "Research the state of AI in 2025". You should see the custom UI component that we added render the tool call and display the arguments that were passed to the tool.

</Step>
</Steps>

## Rendering Arbitrary Tool Calls

When working with agents, they may call tools that you haven't explicitly defined UI components for. You can use a catch-all action to render these tool calls:

```tsx
import {
  useCopilotAction,
  CatchAllActionRenderProps,
} from "@copilotkit/react-core";

useCopilotAction({
  name: "*",
  followUp: false,
  render: ({ name, args, status, result }: CatchAllActionRenderProps<[]>) => {
    return (
      <div className="m-4 p-4 bg-gray-100 rounded shadow">
        <h2 className="text-sm font-medium">Tool: {name}</h2>
        <pre className="mt-2 text-xs overflow-auto">
          {JSON.stringify(args, null, 2)}
        </pre>
        {status === "complete" && (
          <div className="mt-2 text-xs text-green-600">✓ Complete</div>
        )}
      </div>
    );
  },
});
```

This will render any tool call that doesn't have a specific UI component defined for it, displaying the tool name, arguments, and completion status.
