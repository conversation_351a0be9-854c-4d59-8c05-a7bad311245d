---
title: CrewAI Crews
description: Learn how to implement Human-in-the-Loop (HITL) using CrewAI Crews.
icon: lucide/Share2
---

import RunAndConnectAgentSnippet from "@/snippets/coagents/run-and-connect-agent.mdx";  
import InstallSDKSnippet from "@/snippets/install-python-sdk-crew.mdx";

<video
  src="/images/coagents/node-hitl.mp4"
  className="rounded-lg shadow-xl"
  loop
  playsInline
  controls
  autoPlay
  muted
/>

<Callout type="info">  
  Pictured above is the [coagent starter](https://github.com/copilotkit/copilotkit/tree/main/examples/coagent-starter-crewai-crews)  
  with the implementation below applied!  
</Callout>

## What is this?

[Crew-based agents](https://docs.crewai.com/concepts/crews) are stateful agents that support interruption and resumption, enabling user input mid-execution.

CopilotKit provides custom UI hooks to capture that input and feed it back into the agent—making it easy to implement Human-in-the-Loop (HITL) workflows.

> **Important**  
> To implement HITL with CrewAI, you'll need:
> - [CrewAI](https://www.crewai.com/) for managing and orchestrating the agents  
> - [CopilotKit Cloud](https://cloud.copilotkit.ai/) to handle UI workflows and communication

## Why should I use this?

Human-in-the-loop ensures your agent stays on track by letting users approve or modify actions during execution. This is especially useful for production-grade workflows that need more control, auditing, or fine-tuning.

Crew-based agents are ideal for HITL because they preserve execution state and context, even when paused for human input.

## Implementation

<Steps>
  <Step>
    ### Run and connect your agent

    Make sure your agent is running and connected to CopilotKit. If you haven’t set this up, start with the [Getting Started guide](/crewai-crews/quickstart/crewai).

  </Step>


  <Step>
    ### Add a `useCopilotAction` to your frontend

    Create a component that displays the crew’s output and asks the user for approval before proceeding.

    ```tsx title="ui/app/page.tsx"
    import { useCopilotAction } from "@copilotkit/react-core";
    import { Markdown } from "@copilotkit/react-ui";

    function YourMainContent() {
      useCopilotAction({
        name: "crew_requesting_feedback",
        description: "Request feedback from the user on the crew's output",
        renderAndWaitForResponse: ({ args, respond, status }) => (
          <div>
            <pre>{args}</pre>
            <div className={`flex gap-4 pt-4 ${status !== "executing" ? "hidden" : ""}`}>
              <button
                onClick={() => respond?.("Reject")}
                disabled={status !== "executing"}
                className="border p-2 rounded-xl w-full"
              >
                Cancel
              </button>
              <button
                onClick={() => respond?.("Approve")}
                disabled={status !== "executing"}
                className="bg-blue-500 text-white p-2 rounded-xl w-full"
              >
                Approve Kickoff
              </button>
            </div>
          </div>
        ),
      });
    }
    ```
  </Step>

  <Step>
    ### Set up HITL on your Crew

    You’ll need to configure your CrewAI Crew to pause and wait for human input before continuing.

    - Follow this CrewAI guide: [Human Input on Execution](https://docs.crewai.com/how-to/human-input-on-execution)  
    - Reference this working example: [restaurant-finder-crew with HITL](https://github.com/suhasdeshpande/restaurant-finder-crew/tree/main/src/similar_company_finder_template)

    > Reminder: To make this work end-to-end, you must run your crew on [CrewAI](https://www.crewai.com/) and connect it to [CopilotKit Cloud](https://cloud.copilotkit.ai/). These hosted solutions take care of message passing, HITL state syncing, and UI orchestration.
  </Step>

  <Step>
    ### Try it out!

    Run a test prompt like:  
    **"Research the state of AI in 2025."**

    You’ll see your custom UI appear, giving you the chance to approve or cancel the crew’s execution before it continues.
  </Step>
</Steps>
