---
title: Crew-based Chat
icon: "lucide/SendHorizontal"
description: Create interactive chat experiences with CrewAI crews.
---

import ConnectCopilotUI from "@/snippets/copilot-ui.mdx";
import SelfHostingCopilotRuntimeCreateEndpoint from "@/snippets/self-hosting-copilot-runtime-create-endpoint.mdx";
import SelfHostingCopilotRuntimeConfigureCopilotKitProvider from "@/snippets/self-hosting-copilot-runtime-configure-copilotkit-provider.mdx";
import CopilotCloudConfigureCopilotKitProvider from "@/snippets/cloud/cloud-copilotkit-provider.mdx";
import ComponentExamples from "@/snippets/component-examples.mdx";
import { UserIcon, PaintbrushIcon, WrenchIcon, RepeatIcon } from "lucide-react";

<video
  src="/images/coagents/agentic-chat-ui.mp4"
  className="rounded-lg shadow-xl"
  loop
  playsInline
  controls
  autoPlay
  muted
/>
<Callout>
  This video shows the [coagents
  starter](https://github.com/CopilotKit/CopilotKit/tree/main/examples/coagents-starter-crewai-crews)
  repo with various Copilot UI components applied to it!
</Callout>

## What is this?
CopilotKit allows you to chat with an LLM that has the ability to kick off your crews. This is a great way to enable natural conversations that flow
in and out of your crews. In addition to this, this chat serves as a shared communication channel for your executing crews to report their thoughts,
progress, and results.

If you've gone through the [getting started guide](/crewai-crews/quickstart/crewai) **you've already got this working**!

## How does this work?
CopilotKit provides a *"llm-in-the-middle"* chat that will communicate with your user to determine which Crew to kick off and gather the necessary input data
in order to kick off the Crew.

Once the Crew is kicked-off, CopilotKit will handle streaming thoughts, progress, interrupts, and results back to the user.

## When should I use this?
The crew-based chat interface is ideal when you need to:

- **Enable natural conversations with complex crews** - Let users interact with multiple specialized Crews through a single chat interface
- **Support long-running crew tasks** - Provide real-time feedback during research, analysis, and other time-intensive operations
- **Create guided experiences** - Allow your CrewAI crew to ask for inputs, provide outputs, and maintain context throughout an interaction

## Implementation

CopilotKit provides a variety of different batteries-included components to choose from when creating Crew-enabled applications. They scale
from simple chat UIs to completely custom interfaces.

<ComponentExamples components={props.components} />
