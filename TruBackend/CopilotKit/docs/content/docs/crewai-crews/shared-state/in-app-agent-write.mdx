---
title: Setting the inputs
icon: "lucide/ArrowRight"
description: Write to the inputs of a CrewAI Crew from your application.
---

<video
  src="/images/coagents/write-agent-state.mp4"
  className="rounded-lg shadow-xl"
  loop
  playsInline
  controls
  autoPlay
  muted
/>
<Callout>
  This video shows the [coagents
  starter](https://github.com/CopilotKit/CopilotKit/tree/main/examples/coagents-starter-crewai-crews)
  repo with the previous steps applied to it!
</Callout>

## What is this?

This guide shows you how to write to your agent's state from your application.

## When should I use this?

You can use this when you want to provide the user with a way to update the inputs of a CrewAI Crew from your application. CopilotKit allows you to fully customize how these UI components are rendered.

## Implementation

<Steps>
  <Step>
    ### Run and Connect Your Agent to CopilotKit

    You'll need to run your agent and connect it to CopilotKit before proceeding. If you haven't done so already, you can follow the instructions in the [Getting Started](/getting-started) guide.

    If you don't already have an agent, you can use the [coagent starter](https://github.com/copilotkit/copilotkit/tree/main/examples/coagent-starter-crewai-crews) as a starting point
    as this guide uses it as a starting point.

  </Step>
  <Step>
    ### Calling `setState` function from the `useCoAgent` hook
    `useCoAgent` returns a `setState` function that you can use to update the agent state. Calling this will update the agent state and trigger a rerender of anything that depends on the agent state.

    ```tsx title="ui/app/page.tsx"
    import { useCoAgent } from "@copilotkit/react-core"; // [!code highlight]

    // Define the agent state type, should match the actual state of your agent
    type AgentState = {
      language: "english" | "spanish";
    }

    // Example usage in a pseudo React component
    function YourMainContent() {
      const { state, setState } = useCoAgent({ // [!code highlight]
        name: "research_crew",
        initialState: { // optionally provide an initial state
          inputs: {
            topic: "",
            current_year: "2025",
          },
          outputs: "Report will appear here",
        },
      });
      // ...

      return (
        // style excluded for brevity
        <div>
          <label htmlFor="topic">
            Topic
          </label>
          <input
            type="text"
            value={state.inputs.topic}
            onChange={(e) =>
              setState({
                ...state,
                inputs: { ...state.inputs, topic: e.target.value },
              })
            }
          />
        </div>
      );
    }
    ```

  </Step>
  <Step>
    ### Give it a try!
    You can now use the `setState` function to update the crew `inputs` and `state` to read it. Try setting  a topic and talking to your agent.
  </Step>
</Steps>
