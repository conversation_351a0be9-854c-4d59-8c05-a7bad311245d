---
title: "Using Agent Execution Parameters"
icon: "lucide/Bolt"
description: "Using agent execution parameters when communicating with an agent."
---

## What is this?
LangGraph agents are able to take execution parameters, such as auth tokens and similar properties.
You can add these using this feature.

If you wish to read further, you can refer to [the configuration guide by LangGraph](https://langchain-ai.github.io/langgraph/how-tos/configuration/)

## When should I use this?

This is useful when you want to send execution-time configuration information (such as different tokens or metadata for a given session) that should not be part of the agent state.

## Implementation

By default, LangGraph agents are invoked with a `config` argument. This config has a `configurable` property which can be accessed and filled with your data.

<Steps>
<Step>
### Pass configuration from the frontend
First, pass the configuration properties as you would like to receive them in the agent

```tsx title="app/page.tsx"
import { useCoAgent } from "@copilotkit/react-core"; // [!code highlight]

function YourMainContent() {
  // ...

  useCoAgent<AgentState>({
    name: "sample_agent",
    // [!code highlight:7]
    config: {
      configurable: {
        authToken: 'example-token'
      },
      recursion_limit: 50,
    }
  })

  // ...

  return (... your component UI markdown)
}
```
</Step>
<Step>
### Use configurables in agent
Now you can simply pull the values from the provided config argument in any agent node

<Tabs groupId="language" items={['Python', 'TypeScript']} default="Python">
    <Tab value="Python">
        ```python
        async def agent_node(state: AgentState, config: RunnableConfig):
            
            auth_token = config['configurable'].get('authToken', None)

            return state
        ```
    </Tab>
    <Tab value="TypeScript">
        ```typescript
        async function agentNode(state: AgentState, config: RunnableConfig): Promise<AgentState> {
            const authToken = config.configurable?.authToken ?? null;

            return state;
        }
        ```
    </Tab>
</Tabs>
</Step>
<Step>
    ### Optional: Define configurables schema
    If you'd like, you can define a schema to indicate which configurables you wish to receive.
    Any item passed to "configurables" which is not included in the schema, will be filtered out.

    You can read more about this (here)[https://langchain-ai.github.io/langgraph/how-tos/configuration/#define-graph]
    <Tabs groupId="language" items={['Python', 'TypeScript']} default="Python">
        <Tab value="Python">
            ```python
            from typing import TypedDict

            # define which properties will be allowed in the configuration
            class ConfigSchema(TypedDict):
              authToken: str

            # ...add all necessary graph nodes

            # when defining the state graph, apply the config schema
            workflow = StateGraph(AgentState, config_schema=ConfigSchema)
            ```
        </Tab>
        <Tab value="TypeScript">
            ```typescript
            import { Annotation } from "@langchain/langgraph";

            // define which properties will be allowed in the configuration
            export const ConfigSchemaAnnotation = Annotation.Root({
              authToken: Annotation<string>
            })

            // ...add all necessary graph nodes

            // when defining the state graph, apply the config schema
            const workflow = new StateGraph(AgentStateAnnotation, ConfigSchemaAnnotation)
            ```
        </Tab>
    </Tabs>
</Step>
</Steps>
