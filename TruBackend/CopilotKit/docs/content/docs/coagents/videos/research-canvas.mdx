---
title: "Video: Research Canvas"
icon: "lucide/BookOpen"
---
import { YouTubeVideo } from "@/components/react/youtube-video";

<YouTubeVideo videoId="0b6BVqPwqA0" defaultPlaybackRate={1.25} />

This is a step by step walkthrough of building an Agent-Native Research canvas using **CopilotKit and LangGraph**.

You can run the app here: https://examples-coagents-research-canvas-ui.vercel.app/

The full source code is [available on GitHub](https://github.com/CopilotKit/CopilotKit/blob/main/examples/coagents-research-canvas/readme.md).

