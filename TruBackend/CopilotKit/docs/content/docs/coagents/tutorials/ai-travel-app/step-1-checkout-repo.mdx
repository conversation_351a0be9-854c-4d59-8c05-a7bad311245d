---
title: "Step 1: Checkout the repo"
---

<Steps>
<Step>
### Checkout the starting branch
We'll be working with the CopilotKit repository, specifically using a branch called `coagents-travel-tutorial-start`. This branch contains the starting code for our travel app tutorial.

```shell
git clone -b coagents-travel-tutorial-start https://github.com/CopilotKit/CopilotKit.git
cd CopilotKit
```

The tutorial code is located in the `examples/coagents-travel` directory, which contains:
- `ui/`: A NextJS application where we'll integrate our LangGraph agent
- `agent/`: A Python-based LangGraph agent that we'll be enhancing

Go ahead and navigate to the example directory:

```shell
cd examples/coagents-travel
```
</Step>
<Step>
### Install dependencies
First, let's set up the NextJS application. Navigate to the `ui` directory and install the dependencies:

```shell
cd ui
pnpm install
```
</Step>
<Step>
### Start the project
Launch the development server:

```shell
pnpm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see the travel app in action. Take some time to explore the interface and familiarize yourself with its features.
</Step>
</Steps>

Next, let's understand the LangGraph agent and how it works.