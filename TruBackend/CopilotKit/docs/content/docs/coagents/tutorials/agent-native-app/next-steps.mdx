---
title: "Next Steps"
---

This is the end of the tutorial. You now know the basics of how to build complex agentic experiences into your own applications.

## Source code

You can find the source code and interactive sandboxes here:
- [Start app](https://github.com/CopilotKit/open-research-ANA/tree/tutorial-start)
- [Final app](https://github.com/CopilotKit/open-research-ANA/tree/main)

## What's next?

For next steps, here are some ideas:

- Add persistence for [messages](/coagents/persistence/loading-message-history) and [agent state](/coagents/persistence/loading-agent-state).
- Enhance the back-and-fourth with the agent by adding more tools that can update the agent's state.
- Allow the human to ask for inline editing of the research report.

We have more tutorials coming soon, please let us know if you have any ideas for what you'd like to see next!

## Need help?

If you have any questions, feel free to reach out to us on [Discord](https://discord.gg/6dffbvGU3D).
