---
title: Streaming and Tool Calls
description: CoAgents support streaming your messages and tool calls to the frontend.
---

If you'd like to change how LangGraph agents behave as CoAgents you can utilize our Copilotkit SDK which provides a collection
of functions and utilities for interacting with the agent's state or behavior. One example of this is the Copilotkit config 
which is a wrapper of the LangGraph `config` object. This allows you to extend the configuration of your LangGraph nodes to 
change how LangGraph and Copilotkit interact with each other. This allows you to change how messages and tool calls are emitted and
streamed to the frontend.

## Message Streaming
If you did not change anything in your LangGraph node, message streaming will be on by default. This allows for a message to be
streamed to Copilotkit as it is being generated, allowing for a more responsive experience. However, you can disable this if you
want to have the message only be sent after the agent has finished generating it.

```python
config = copilotkit_customize_config(
    config,
    # True or False
    emit_messages=False,
)
```

## Emitting Tool Calls
Emission of tool calls are off by default. This means that tool calls will not be sent to Copilotkit for processing and rendering.
However, within a node you can extend the LangGraph `config` object to emit tool calls to Copilotkit. This is useful in situations
where you may to emit what a potential tool call will look like prior to being executed.

```python
config = copilotkit_customize_config(
    config,
    # Can set to True, False, or a list of tool call names to emit.
    emit_tool_calls=["tool_name"],
)
```

For more information on how tool calls are utilized check out our [frontend actions](/coagents/frontend-actions)
documentation.
