---
title: Agentic Copilots
description: Agentic copilots provide you with advanced control and orchestration over your agents.
icon: lucide/Bot
---

import { TailoredContent, TailoredContentOption } from "@/components/react/tailored-content";
import { BsFillCloudHaze2Fill as CloudIcon } from "react-icons/bs";
import { FaServer as SelfHostIcon } from "react-icons/fa6";
import { SiLangchain } from "react-icons/si";
import { LinkIcon } from "lucide-react";
import { RocketIcon, GraduationCapIcon, CodeIcon, VideoIcon } from "lucide-react";

Before we dive into what agentic copilots are, help us help you by telling us your level of experience with LangGraph. We'll explain things in a way that best suits your experience level.

<TailoredContent id="experience" defaultOptionIndex={0}>
    <TailoredContentOption 
        id="new"
        title="I'm new to LangGraph" 
        description="Help me understand what agentic copilots are, where <PERSON><PERSON><PERSON><PERSON> fits in, and how to get started." 
        icon={<img src="/images/copilotkit-logo.svg" width={7} height={7} />}
    >
        <Frame>
            <img src="/images/coagents/SharedStateCoAgents.gif" alt="CoAgents Shared State" className="mt-0 mb-12"/>
        </Frame>

        ### What are Agents?
        AI agents are intelligent systems that interact with their environment to achieve specific goals. Think of them as 'virtual colleagues' that can handle tasks ranging from 
        simple queries like "find the cheapest flight to Paris" to complex challenges like "design a new product layout."

        As these AI-driven experiences (or 'Agentic Experiences') become more sophisticated, developers need finer control over how agents make decisions. This is where specialized 
        frameworks like LangGraph become essential.

        ### What is LangGraph?
        LangGraph is a framework that gives you precise control over AI agents. It uses a graph-based approach where each step in an agent's decision-making process is represented 
        by a `node`. These nodes are connected by `edges` to form a directed acyclic graph (DAG), creating a clear map of possible actions and decisions.

        The key advantage of LangGraph is its tight control over the agent's decision making process. Since all of this is defined in code by you, the behavior is much more
        deterministic and predictable.

        ### What are Agentic Copilots?
        Agentic copilots are how CopilotKit brings LangGraph agents into your application. If you're familiar with CopilotKit, you know that copilots are AI assistants that 
        understand your app's context and can take actions within it. While CopilotKit's standard copilots use a simplified [ReAct pattern](https://www.perplexity.ai/search/what-s-a-react-agent-5hu7ZOaKSAuY7YdFjQLCNQ) 
        for quick implementation, Agentic copilots give you LangGraph's full orchestration capabilities when you need more control over your agent's behavior.

        ### What are CoAgents?
        CoAgents are what we call CopilotKit's approach to building agentic experiences! They're interchangeable with agentic copilots being a more descriptive term for the overall concept.

        ### When should I use CopilotKit's CoAgents?
        You should use CoAgents when you require tight control over the Agentic runloop, as facilitated by an Agentic Orchestration framework like [LangGraph](https://langchain-ai.github.io/langgraph/).
        With CoAgents, you can carry all of your existing CopilotKit-enabled Copilot capabilities into a customized agentic runloop.

        We suggest beginning with a basic Copilot and gradually transitioning specific components to CoAgents.
        
        The need for CoAgents spans a broad spectrum across different applications. At one end, their advanced capabilities might not be required at all, or only for a minimal 10% of the application's 
        functionality. Progressing further, there are scenarios where they become increasingly vital, managing 60-70% of operations. Ultimately, in some cases, CoAgents are indispensable, orchestrating
        up to 100% of the Copilot's tasks (see [agent-lock mode](/coagents/multi-agent-flows) for the 100% case).

        ### Examples
        An excellent example of the type of experiences you can accomplish with CoAgents applications can be found in our [Research Canvas](/coagents/videos/research-canvas).
        
        More specifically, it demonstrates how CoAgents allow for AI driven experiences with:
        - Precise state management across agent interactions
        - Sophisticated multi-step reasoning capabilities
        - Seamless orchestration of multiple AI tools
        - Interactive human-AI collaboration features
        - Real-time state updates and progress streaming

        ## Next Steps

        Want to get started? You have some options!

        <Cards>
            <Card
                title="Build your first CoAgent"
                description="Follow a step-by-step tutorial to build a travel app supercharged with CoAgents."
                href="/coagents/quickstart/langgraph"
                icon={<RocketIcon />}
            />
            <Card
                title="Learn more CoAgent concepts"
                description="Learn more about the concepts used to talk about CoAgents and how to use them."
                href="/coagents/concepts/terminology"
                icon={<GraduationCapIcon />}
            />
            <Card
                title="Read the reference documentation"
                description="Just here for some reference? Checkout the reference documentation for more details."
                href="/reference"
                icon={<CodeIcon />}
            />
            <Card
                title="See examples of CoAgents in action"
                description="Checkout our video examples of CoAgents in action."
                href="/coagents/videos/research-canvas"
                icon={<VideoIcon />}
            />
        </Cards>
    </TailoredContentOption>
    <TailoredContentOption 
        id="intermediate"
        title="I'm already using LangGraph" 
        description="Help me understand what agentic copilots are, what Copilotkit does to integrate with LangGraph, and how to get started." 
        icon={<SiLangchain />}
    >

        <Frame className="mt-0 mb-12">
            <img
                src="/images/CoAgents.gif"
                alt="CoAgents demonstration"
                className="w-auto"
            />
        </Frame>

        LangChain's LangGraph is a framework for building deeply customizable AI agents.

        CopilotKit's Agentic Copilots is infrastruture for in-app agent-user interaction, i.e. for transforming agents from autonomous processes to user-interactive 'virtual colleagues' that live inside applications.

        Any LangGraph-based agent can be transformed into an Agentic Copilot with a minimal amount
        of effort to get industry leading agnetic UX such as:
        - Shared state between the agent and the application.
        - Intermediate result and state progress streaming
        - Human-in-the-loop collaboration
        - Agentic generative UI
        - And more!

        All of these features are essential to delight instead of frustrate your users with AI features.

        ### What are CoAgents?
        CoAgents are what we call CopilotKit's approach to building agentic experiences! They're interchangeable with agentic copilots being a more descriptive term for the overall concept.

        ## Next Steps
        Want to get started? You have some options!

        <Cards>
            <Card
                title="Quickstart"
                description="Integrate your LangGraph agent with CopilotKit in a few minutes."
                href="/coagents/quickstart/langgraph"
                icon={<RocketIcon />}
            />
            <Card
                title="Tutorial: AI Travel App"
                description="Follow a step-by-step tutorial to build a travel app supercharged with CoAgents."
                href="/coagents/tutorials/ai-travel-app/overview"
                icon={<GraduationCapIcon />}
            />
            <Card
                title="Reference"
                description="Learn more about the terms used to talk about CoAgents and how to use them."
                href="/reference"
                icon={<CodeIcon />}
            />
            <Card
                title="Examples"
                description="Checkout our video examples of CoAgents in action."
                href="/coagents/videos/research-canvas"
                icon={<VideoIcon />}
            />
        </Cards>
    </TailoredContentOption>
</TailoredContent>
