---
title: Reading agent state
icon: "lucide/ArrowLeft"
description: Read the realtime agent state in your native application.
---

import { <PERSON>Zoom } from "fumadocs-ui/components/image-zoom";

<Frame>
  <ImageZoom
    src="/images/coagents/read-agent-state.png"
    alt="read agent state"
    width={1000}
    height={1000}
    className="my-0"
  />
</Frame>

<Callout type="info">
  Pictured above is the [coagent
  starter](https://github.com/copilotkit/copilotkit/tree/main/examples/coagent-starter)
  with the [implementation](#implementation) section applied!
</Callout>

## What is this?

You can easily use the realtime agent state not only in the chat UI, but also in the native application UX.

## When should I use this?

You can use this when you want to provide the user with feedback about what your agent's state. As your agent's
state update you can reflect these updates natively in your application.

## Implementation

<Steps>
  <Step>
    ### Run and Connect Your Agent to CopilotKit

    You'll need to run your agent and connect it to CopilotKit before proceeding. If you haven't done so already,
    you can follow the instructions in the [Getting Started](/getting-started) guide.

    If you don't already have an agent, you can use the [coagent starter](https://github.com/copilotkit/copilotkit/tree/main/examples/coagent-starter) as a starting point
    as this guide uses it as a starting point.

  </Step>
  <Step>
    ### Define the Agent State
    --YOUR-FRAMEWORK-- Agents are stateful. As you transition through the flow, that state is updated and available to the next function. For this example,
    let's assume that our agent state looks something like this.

    <Tabs groupId="language" items={["Python"]}>
      <Tab value="Python">
        ```python title="agent-py/sample_agent/agent.py"
        # your code goes here...
        ```
      </Tab>
    </Tabs>

  </Step>
  <Step>
    ### Use the `useCoAgent` Hook
    With your agent connected and running all that is left is to call the [useCoAgent](/reference/hooks/useCoAgent) hook, pass the agent's name, and
    optionally provide an initial state.

    ```tsx title="ui/app/page.tsx"
    import { useCoAgent } from "@copilotkit/react-core"; // [!code highlight]

    // Define the agent state type, should match the actual state of your agent
    type AgentState = {
      language: "english" | "spanish";
    }

    function YourMainContent() {
      const { state } = useCoAgent<AgentState>({ // [!code highlight:4]
        name: "sample_agent",
        initialState: { language: "spanish" }  // optionally provide an initial state
      });

      // ...

      return (
        // style excluded for brevity
        <div>
          <h1>Your main content</h1>
          <p>Language: {state.language}</p> // [!code highlight]
        </div>
      );
    }
    ```
    <Callout type="info">
      The `state` in `useCoAgent` is reactive and will automatically update when the agent's state changes.
    </Callout>

  </Step>
  <Step>
    ### Give it a try!
    As the agent state updates, your `state` variable will automatically update with it! In this case, you'll see the
    language set to "spanish" as that's the initial state we set.
  </Step>
</Steps>

## Rendering agent state in the chat

You can also render the agent's state in the chat UI. This is useful for informing the user about the agent's state in a
more in-context way. To do this, you can use the [useCoAgentStateRender](/reference/hooks/useCoAgentStateRender) hook.

```tsx title="ui/app/page.tsx"
import { useCoAgentStateRender } from "@copilotkit/react-core"; // [!code highlight]

// Define the agent state type, should match the actual state of your agent
type AgentState = {
  language: "english" | "spanish";
};

function YourMainContent() {
  // ...
  // [!code highlight:8]
  useCoAgentStateRender({
    name: "sample_agent",
    render: ({ state }) => {
      if (!state.language) return null;
      return <div>Language: {state.language}</div>;
    },
  });
  // ...
}
```

<Callout type="info">
  The `state` in `useCoAgentStateRender` is reactive and will automatically
  update when the agent's state changes.
</Callout>

## Intermediately Stream and Render Agent State

By default, the --YOUR-FRAMEWORK-- Agent state will only update _between_ --YOUR-FRAMEWORK-- Agent node transitions --
which means state updates will be discontinuous and delayed.

You likely want to render the agent state as it updates **continuously.**

See **[emit intermediate state](/--YOUR-FRAMEWORK--/shared-state/predictive-state-updates).** 