---
title: Terminology
icon: lucide/Book
---

Here are the key terms and concepts used throughout CoAgents:

| Term                         | Definition                                                                                                                                                                                         |
| ---------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Agentic Copilot              | An AI agent designed to collaborate with users in Agent-Native applications, rather than operate autonomously.                                                                                     |
| CoAgent                      | Terminology referring to CopilotKit's suite of tools for building agentic applications. Typically interchangeable with agentic copilot.                                                            |
| Agentic Generative UI        | UI components that are dynamically generated and updated based on the agent's current state, providing users with visibility into what the agent is doing and building trust through transparency. |
| Ground Truth                 | In CoAgents, CopilotKit serves as the "ground truth" for the full chat session, maintaining the persistent chat history and ensuring conversational continuity across different agents.            |
| Human-in-the-Loop (HITL)     | A workflow pattern where human input or validation is required during agent execution, enabling quality control and oversight at critical decision points.                                         |
| [Mastra](https://mastra.ai) | The agent framework integrated with CopilotKit that provides the orchestration layer for CoAgents, enabling sophisticated multi-step reasoning and state management.                               |
| Agent Lock Mode              | A mode where CopilotKit is configured to work exclusively with a specific agent, ensuring all requests stay within a single workflow graph for precise control.                                    |
| Router Mode                  | A mode where CopilotKit dynamically routes requests between different agents and tools based on context and user input, enabling flexible multi-agent workflows.                                   |

These terms are referenced throughout the documentation and are essential for understanding how CoAgents work and how to implement them effectively in your applications. 