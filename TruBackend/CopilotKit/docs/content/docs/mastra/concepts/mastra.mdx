---
title: Mastra
description: An agentic framework for building LLM applications that can be used with Copilotkit.
icon: custom/mastra
---

Mastra is an agentic framework for building LLM applications that can be used with Copilotkit. Mastra Agents allow developers
to combine and coordinate tasks efficiently, providing a robust framework for building sophisticated AI automations.

## CoAgents and Mastra

How do CoAgents extend Mastra?


- **Multi-actor**: CoAgents allow for multiple agents to interact with each other. Copilotkit acts as the "ground-truth"
  when transitioning between agents. Read more about how multi-actor workflows work [here](/mastra/multi-agent-flows)
  and how messages are managed [here](/mastra/concepts/message-management).
- **LLMs**: CoAgents use large language models to generate responses. This is useful for building applications that need to
  generate natural language responses.
- **Human in the loop**: CoAgents enabled human review and approval of generated responses. Read more about how this works
  [here](/mastra/human-in-the-loop).
- **Tool calling**: Tool calling is a fundamental building block for agentic workflows. They allow for greater control over what
  the agent can do and can be used to interact with external systems. CoAgents allow you to easily render in-progress
  tool calls in the UI so your users know what's happening. Read more about streaming tool calls [here](/mastra/frontend-actions).

## Building with TypeScript

You can build Mastra applications using TypeScript. Check out the [Mastra docs](https://mastra.ai/en/docs) for more information.

## Mastra Cloud

Mastra Cloud is a platform for deploying and monitoring Mastra applications. Read more about it on the
[Mastra website](https://mastra.ai/cloud-beta).

If you want to take the next step to deploy your Mastra application as an CoAgent, check out our [quickstart guide](/mastra/quickstart/mastra). 