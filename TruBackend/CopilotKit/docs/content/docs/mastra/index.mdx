---
title: Introduction
icon: "lucide/Sparkles"
description: Build Agent-Native Applications (ANAs) powered by CopilotKit and Mastra Agents.
---

import { BiSolidMessage as TextIcon } from "react-icons/bi";
import { Vsc<PERSON>son as JsonIcon } from "react-icons/vsc";
import { FaDiscord } from "react-icons/fa";
import Link from "next/link";
import { TelescopeIcon } from "lucide-react";
import { YouTubeVideo } from "@/components/react/youtube-video";
import { CoAgentsEnterpriseCTA } from "@/components/react/coagents/coagents-enterprise-cta.tsx";
import {
  CoAgentsFeatureToggle,
  CoAgentsFeatureRender,
} from "@/components/react/coagents/coagents-features.tsx";
import { DynamicContentWrapper } from "@/components/react/dynamic-content-wrapper";
import { ExamplesCarousel } from "@/components/react/examples-carousel";
import {
  LuPlane,
  LuBookOpen,
  LuLightbulb,
  LuLayoutTemplate,
  <PERSON><PERSON>rain<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Lu<PERSON>essage<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "react-icons/lu";
import { CoAgentsExamples } from "@/components/react/examples-carousel";
import { CTACards } from "@/components/react/cta-cards";
import { FaSync } from "react-icons/fa";
import { Socials } from "@/components/react/socials";

<div className="p-4 mb-6 rounded-lg bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
  <div className="flex items-center gap-2 mb-2">
    <TelescopeIcon className="h-5 w-5 text-gray-700 dark:text-gray-300" />
    <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300">Mastra Overview</h3>
  </div>
  <p className="text-gray-700 dark:text-gray-300">
    Visit the <a href="https://v0-mastra-land.vercel.app/" target="_blank" rel="noopener noreferrer" className="font-medium underline underline-offset-4 decoration-gray-400 dark:decoration-gray-500 hover:text-gray-900 dark:hover:text-gray-100">Mastra Overview Page</a> to learn more about Mastra's capabilities and features.
  </p>
</div>

# Copilot Infrastructure for Mastra Agents

Full user-interaction infrastructure for your agents, to turn your agents into Copilot Agents (CoAgents).


## Building blocks of a CoAgent

Everything you need to build Agent-Native Applications (ANAs), right out of the box.

<Cards className="gap-6">
  <Card
    className="p-6 rounded-xl text-base border-black dark:border-white"
    icon={<LuMessageSquare />}
    title="Agentic Chat UI"
    description="In-app chat powered by your agent."
    href="/mastra/agentic-chat-ui"
  />
  <Card
    className="p-6 rounded-xl text-base border-black dark:border-white"
    icon={<LuLayoutTemplate />}
    title="Generative UI"
    description="UI that updates in real-time based on your agent's state."
    href="/mastra/generative-ui"
  />
  <Card
    className="p-6 rounded-xl text-base border-black dark:border-white"
    icon={<LuWand2 />}
    title="Frontend Tools"
    description="Give your agent the ability to take action in your application."
    href="/mastra/frontend-actions"
  />
  <Card
    className="p-6 rounded-xl text-base border-black dark:border-white"
    icon={<LuWand2 />}
    title="Multi-Agent Coordination"
    description="Route your agent to the right agent based on the user's request."
    href="/mastra/multi-agent-flows"
  />
  <Card
    className="p-6 rounded-xl text-base border-black dark:border-white"
    icon={<LuUserCog />}
    title="Human-in-the-Loop"
    description="Set smart checkpoints where humans can guide your agents."
    href="/mastra/human-in-the-loop"
  />
</Cards>

## Ready to get started?

Get started with CoAgents in as little as 5 minutes with one of our guides or tutorials.

<Cards className="gap-6">
  <Card
    className="p-6 rounded-xl text-base border-black dark:border-white"
    icon={<LuPlay />}
    title="Quickstart"
    description="Learn how to build your first CoAgent in 10 minutes."
    href="/mastra/quickstart"
  />
  <Card
    className="p-6 rounded-xl text-base border-black dark:border-white"
    icon={<TelescopeIcon />}
    title="Feature Overview"
    description="Try the key features of CoAgents powered by Mastra Agents."
    href="https://copilotkit-mastra-feature-viewer.vercel.app/"
    target="_blank"
  />
</Cards>

{/* TODO: Add example tutorials for Mastra Agents */}

## Common Questions

Have a question about CoAgents? You're in the right place!

<Accordions>
<Accordion title="Can you explain what a CoAgent is in more detail?">
Sure! CoAgents are what we call "agentic copilots". Well, what's an agentic copilot then?

Think of a Copilot as a simple and fully LLM controlled assistant that has relatively limited capabilities. An Agentic Copilot then is a copilot
that has been enhanced with the ability to use Mastra agents to perform more complex tasks. This is an extremely powerful way to build AI
powered applications because it gives you, the developer, the ability to control the agent's behavior in a deterministic way while still letting
the agent do its magic.

For more on this topic, checkout our [agentic copilot](/mastra/concepts/agentic-copilots) concept page.

</Accordion>
</Accordions> 