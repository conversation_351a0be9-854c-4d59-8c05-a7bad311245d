---
title: Quickstart
description: Turn your Mastra Agents into an agent-native application in 10 minutes.
icon: "lucide/Play"
---

import {
  TailoredContent,
  TailoredContentOption,
} from "@/components/react/tailored-content.tsx";
import { CoAgentsEnterpriseCTA } from "@/components/react/coagents/coagents-enterprise-cta.tsx";
import { CoAgentsDiagram } from "@/components/react/coagents/coagents-diagram.tsx";
import { FaPython, FaJs, FaCloud } from "react-icons/fa";
import SelfHostingCopilotRuntimeCreateEndpoint from "@/snippets/self-hosting-copilot-runtime-create-endpoint.mdx";
import CopilotCloudConfigureRemoteEndpointLangGraph from "@/snippets/copilot-cloud-configure-remote-endpoint-langgraph.mdx";
import CopilotKitCloudCopilotKitProvider from "@/snippets/copilot-cloud-configure-copilotkit-provider.mdx";
import LangGraphPlatformDeploymentTabs from "@/snippets/langgraph-platform-deployment-tabs.mdx";
import { Accordions, Accordion } from "fumadocs-ui/components/accordion";
import FindYourCopilotRuntime from "@/snippets/find-your-copilot-runtime.mdx";
import CloudCopilotKitProvider from "@/snippets/coagents/cloud-configure-copilotkit-provider.mdx";
import SelfHostingCopilotRuntimeConfigureCopilotKitProvider from "@/snippets/coagents/self-host-configure-copilotkit-provider.mdx";
import SelfHostingCopilotRuntimeLangGraphEndpoint from "@/snippets/self-hosting-copilot-runtime-langgraph-endpoint.mdx";
import SelfHostingCopilotRuntimeStarter from "@/snippets/self-hosting-copilot-runtime-starter.mdx";
import SelfHostingRemoteEndpoints from "@/snippets/self-hosting-remote-endpoints.mdx";
import {
  UserIcon,
  PaintbrushIcon,
  WrenchIcon,
  RepeatIcon,
  ServerIcon,
} from "lucide-react";
import CopilotUI from "@/snippets/copilot-ui.mdx";

<video
  src="/images/coagents/chat-example.mp4"
  className="rounded-lg shadow-xl"
  loop
  playsInline
  controls
  autoPlay
  muted
/>

## Prerequisites

Before you begin, you'll need the following:

- [**OpenAI API key**](https://platform.openai.com/api-keys)

## Getting started

<Steps>
    <Step>
        ### Install CopilotKit
        First, install the latest packages for CopilotKit into your frontend.
        ```package-install
        npm install @copilotkit/react-ui @copilotkit/react-core
        ```
    </Step>
    <TailoredContent
        className="step"
        id="agent"
        header={
            <div>
                <p className="text-xl font-semibold">Do you already have a Mastra Agent?</p>
                <p className="text-base">
                    You will need a Mastra Agent to get started!
                </p>
                <p className="text-base">
                    Either bring your own or feel free to use our starter repo.
                </p>
            </div>
        }
    >
        <TailoredContentOption
            id="bring-your-own"
            title="Bring your own Mastra Agent"
            description="I already have a Mastra Agent and want to use it with CopilotKit."
            icon={<img src="/images/copilotkit-logo.svg" alt="CopilotKit Logo" width={20} height={20} />}
        />
        <TailoredContentOption
            id="coagents-starter"
            title="Use the CoAgents Starter repo"
            description="I don't have a Mastra Agent yet, but want to get started quickly."
            icon={<img src="/images/copilotkit-logo.svg" alt="CopilotKit Logo" width={20} height={20} />}
        >
            <Step>
                ### Clone the `mastra/starter` repo

                ```bash
                git clone https://github.com/CopilotKit/CopilotKit
                cd CopilotKit/examples/mastra/starter
                ```
            </Step>
            <Step>
                ### Create a `.env` file

                ```bash
                touch .env
                ```
            </Step>
            <Step>
                ### Add your API keys

                Then add your **OpenAI API key** to the `.env` file.

                ```plaintext title=".env"
                OPENAI_API_KEY=your_openai_api_key
                ```
            </Step>
        </TailoredContentOption>
    </TailoredContent>
    <Step>
        ### Launch your local agent

        Start your local Mastra Agent:

        ```bash
        mastra dev
        ```
        This will start a local agent server that you can connect to.
    </Step>
    <Step>
        ### Install Copilot Runtime
        Copilot Runtime is a production-ready proxy for your Mastra Agents. In your frontend, go ahead and install it.

        ```package-install
        @copilotkit/runtime
        ```
    </Step>
    <Step>
        ### Setup a Copilot Runtime Endpoint
        Now we need to setup a Copilot Runtime endpoint and point your frontend to it.
        ```ts
        import {
            CopilotRuntime,
            ExperimentalEmptyAdapter,
            copilotRuntimeNextJSAppRouterEndpoint,
            langGraphPlatformEndpoint
        } from "@copilotkit/runtime";;
        import { NextRequest } from "next/server";
        
        // You can use any service adapter here for multi-agent support.
        const serviceAdapter = new ExperimentalEmptyAdapter();
        
        const runtime = new CopilotRuntime({
            agents: {
                // added in next step...
            },
        });
        
        export const POST = async (req: NextRequest) => {
        const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
            runtime,
            serviceAdapter,
            endpoint: "/api/copilotkit",
        });
        
        return handleRequest(req);
        };
        ```
    </Step>
    <Step>
        ### Add your Mastra Agent deployment to Copilot Runtime
        Now we need to add your Mastra Agent deployment to Copilot Runtime. This will make it
        so your frontend can find your Mastra Agents correctly.
        ```ts
        // ...
        import { MastraClient } from "@mastra/client-js";
        // ...
        const mastra = new MastraClient({
            "http://localhost:4111",
        });
        const mastraAgents = await mastra.getAGUI();
        const runtime = new CopilotRuntime({
            agents: mastraAgents,
        });
        // ...
        ```
    </Step>
    <Step>
        ### Configure the CopilotKit Provider
        The [`<CopilotKit>`](/reference/components/CopilotKit) component must wrap the Copilot-aware parts of your application. For most use-cases,
        it's appropriate to wrap the CopilotKit provider around the entire app, e.g. in your layout.tsx.
        <SelfHostingCopilotRuntimeConfigureCopilotKitProvider components={props.components}/>
        <Callout type="info">
            Looking for a way to run multiple Mastra Agents? Check out our [Multi-Agent](/mastra/multi-agent-flows) guide.
        </Callout>
    </Step>
    <Step>
        ### Setup the Copilot UI
        The last step is to use CopilotKit's UI components to render the chat interaction with your agent. In most situations,
        this is done alongside your core page components, e.g. in your `page.tsx` file.

        ```tsx title="page.tsx"
        // [!code highlight:3]
        import "@copilotkit/react-ui/styles.css";
        import { CopilotPopup } from "@copilotkit/react-ui";

        export function YourApp() {
          return (
            <main>
              <h1>Your main content</h1>
              // [!code highlight:7]
              <CopilotPopup
                labels={{
                    title: "Popup Assistant",
                    initial: "Hi! I'm connected to an agent. How can I help?",
                }}
              />
            </main>
          );
        }
        ```

        <Callout type="info">
            Looking for other chat component options? Check out our [Agentic Chat UI](/mastra/agentic-chat-ui) guide.
        </Callout>
    </Step>
    <Step>
        ### 🎉 Talk to your agent!

        Congrats! You've successfully integrated a Mastra Agent chatbot to your application. To start, try asking a few questions to your agent.

        ```
        Can you tell me a joke?
        ```

        ```
        Can you help me understand AI?
        ```

        ```
        What do you think about React?
        ```

        <video src="/images/coagents/chat-example.mp4" className="rounded-lg shadow-xl" loop playsInline controls autoPlay muted />

        <Accordions className="mb-4">
            <Accordion title="Having trouble?">
                - Try changing the host to `0.0.0.0` or `127.0.0.1` instead of `localhost`.
            </Accordion>
        </Accordions>
    </Step>

</Steps>

---

## What's next?

You've now got a Mastra Agent running in CopilotKit! Now you can start exploring the various ways that CopilotKit
can help you build power agent native applications.

<Cards>
  <Card
    title="Implement Human in the Loop"
    description="Allow your users and agents to collaborate together on tasks."
    href="/mastra/human-in-the-loop"
    icon={<UserIcon />}
  />
  <Card
    title="Add some generative UI"
    description="Render your agent's progress and output in the UI."
    href="/mastra/generative-ui"
    icon={<PaintbrushIcon />}
  />
  <Card
    title="Setup frontend actions"
    description="Give your agent the ability to call frontend tools, directly updating your application."
    href="/mastra/frontend-actions"
    icon={<WrenchIcon />}
  />
</Cards> 