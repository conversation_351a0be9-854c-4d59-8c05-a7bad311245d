---
title: "API Reference"
description: "API Reference for CopilotKit's components, classes and hooks."
---

import { LinkIcon } from "lucide-react";

<Cards>
  <Card
    title="UI Components"
    description="See the list of all available UI components in CopilotKit."
    href="/reference/components/chat/CopilotChat"
    icon={<LinkIcon />}
  />
  <Card
    title="Hooks"
    description="See the list of all available hooks in CopilotKit."
    href="/reference/hooks/useCopilotReadable"
    icon={<LinkIcon />}
  />
  <Card
    title="Classes"
    description="See the list of all available classes in CopilotKit."
    href="/reference/classes/CopilotRuntime"
    icon={<LinkIcon />}
  />
  <Card
    title="LLM Adapters"
    description="See the list of all available LLM Adapters in CopilotKit."
    href="/reference/classes/llm-adapters/OpenAIAdapter"
    icon={<LinkIcon />}
  />
  <Card
    title="SDKs"
    description="Python and JavaScript SDKs for CopilotKit."
    href="/reference/sdk/python/LangGraph"
    icon={<LinkIcon />}
  />
</Cards>
