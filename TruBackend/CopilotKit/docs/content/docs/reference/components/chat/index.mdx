---
title: All Chat Components
---
import { LinkIcon } from "lucide-react"

<Cards>
  <Card
    title="<CopilotChat />"
    description="The CopilotChat component, providing a chat interface for interacting with your copilot."
    href="/reference/components/chat/CopilotChat"
    icon={<LinkIcon />}
  />
  <Card
    title="<CopilotPopup />"
    description="The CopilotPopup component, providing a popup interface for interacting with your copilot."
    href="/reference/components/chat/CopilotPopup"
    icon={<LinkIcon />}
  />
  <Card
    title="<CopilotSidebar />"
    description="The CopilotSidebar component, providing a sidebar interface for interacting with your copilot."
    href="/reference/components/chat/CopilotSidebar"
    icon={<LinkIcon />}
  />
</Cards>

