---
title: "CopilotSidebar"
description: "The CopilotSidebar component, providing a sidebar interface for interacting with your copilot."
---

{
 /*
  * ATTENTION! DO NOT MODIFY THIS FILE!
  * This page is auto-generated. If you want to make any changes to this page, changes must be made at:
  * CopilotKit/packages/react-ui/src/components/chat/Sidebar.tsx
  */
}
<br/>
<img src="/images/CopilotSidebar.gif" width="500" />
 
A chatbot sidebar component for the CopilotKit framework. Highly customizable through various props and custom CSS.
 
See [CopilotPopup](/reference/components/chat/CopilotPopup) for a popup version of this component.
 
## Install Dependencies
 
This component is part of the [@copilotkit/react-ui](https://npmjs.com/package/@copilotkit/react-ui) package.
 
```shell npm2yarn \"@copilotkit/react-ui"\
npm install @copilotkit/react-core @copilotkit/react-ui
```
 
## Usage
 
```tsx
import { CopilotSidebar } from "@copilotkit/react-ui";
import "@copilotkit/react-ui/styles.css";
 
<CopilotSidebar
  labels={{
    title: "Your Assistant",
    initial: "Hi! 👋 How can I assist you today?",
  }}
>
  <YourApp/>
</CopilotSidebar>
```
 
### Look & Feel
 
By default, CopilotKit components do not have any styles. You can import CopilotKit's stylesheet at the root of your project:
```tsx title="YourRootComponent.tsx"
...
import "@copilotkit/react-ui/styles.css"; // [!code highlight]
 
export function YourRootComponent() {
  return (
    <CopilotKit>
      ...
    </CopilotKit>
  );
}
```
For more information about how to customize the styles, check out the [Customize Look & Feel](/guides/custom-look-and-feel/customize-built-in-ui-components) guide.

## Properties

<PropertyReference name="instructions" type="string"  > 
Custom instructions to be added to the system message. Use this property to
  provide additional context or guidance to the language model, influencing
  its responses. These instructions can include specific directions,
  preferences, or criteria that the model should consider when generating
  its output, thereby tailoring the conversation more precisely to the
  user's needs or the application's requirements.
</PropertyReference>

<PropertyReference name="onInProgress" type="(inProgress: boolean) => void"  > 
A callback that gets called when the in progress state changes.
</PropertyReference>

<PropertyReference name="onSubmitMessage" type="(message: string) => void | Promise<void>"  > 
A callback that gets called when a new message it submitted.
</PropertyReference>

<PropertyReference name="onStopGeneration" type="OnStopGeneration"  > 
A custom stop generation function.
</PropertyReference>

<PropertyReference name="onReloadMessages" type="OnReloadMessages"  > 
A custom reload messages function.
</PropertyReference>

<PropertyReference name="onRegenerate" type="(messageId: string) => void"  > 
A callback function to regenerate the assistant's response
</PropertyReference>

<PropertyReference name="onCopy" type="(message: string) => void"  > 
A callback function when the message is copied
</PropertyReference>

<PropertyReference name="onThumbsUp" type="(message: string) => void"  > 
A callback function for thumbs up feedback
</PropertyReference>

<PropertyReference name="onThumbsDown" type="(message: string) => void"  > 
A callback function for thumbs down feedback
</PropertyReference>

<PropertyReference name="markdownTagRenderers" type="ComponentsMap"  > 
A list of markdown components to render in assistant message.
  Useful when you want to render custom elements in the message (e.g a reference tag element)
</PropertyReference>

<PropertyReference name="icons" type="CopilotChatIcons"  > 
Icons can be used to set custom icons for the chat window.
</PropertyReference>

<PropertyReference name="labels" type="CopilotChatLabels"  > 
Labels can be used to set custom labels for the chat window.
</PropertyReference>

<PropertyReference name="imageUploadsEnabled" type="boolean"  > 
Enable image upload button (image inputs only supported on some models)
</PropertyReference>

<PropertyReference name="inputFileAccept" type="string"  > 
The 'accept' attribute for the file input used for image uploads.
  Defaults to "image".
</PropertyReference>

<PropertyReference name="makeSystemMessage" type="SystemMessageFunction"  > 
A function that takes in context string and instructions and returns
  the system message to include in the chat request.
  Use this to completely override the system message, when providing
  instructions is not enough.
</PropertyReference>

<PropertyReference name="AssistantMessage" type="React.ComponentType<AssistantMessageProps>"  > 
A custom assistant message component to use instead of the default.
</PropertyReference>

<PropertyReference name="UserMessage" type="React.ComponentType<UserMessageProps>"  > 
A custom user message component to use instead of the default.
</PropertyReference>

<PropertyReference name="Messages" type="React.ComponentType<MessagesProps>"  > 
A custom Messages component to use instead of the default.
</PropertyReference>

<PropertyReference name="RenderTextMessage" type="React.ComponentType<RenderMessageProps>"  > 
A custom RenderTextMessage component to use instead of the default.
</PropertyReference>

<PropertyReference name="RenderActionExecutionMessage" type="React.ComponentType<RenderMessageProps>"  > 
A custom RenderActionExecutionMessage component to use instead of the default.
</PropertyReference>

<PropertyReference name="RenderAgentStateMessage" type="React.ComponentType<RenderMessageProps>"  > 
A custom RenderAgentStateMessage component to use instead of the default.
</PropertyReference>

<PropertyReference name="RenderResultMessage" type="React.ComponentType<RenderMessageProps>"  > 
A custom RenderResultMessage component to use instead of the default.
</PropertyReference>

<PropertyReference name="RenderImageMessage" type="React.ComponentType<RenderMessageProps>"  > 
A custom RenderImageMessage component to use instead of the default.
</PropertyReference>

<PropertyReference name="RenderSuggestionsList" type="React.ComponentType<RenderSuggestionsListProps>"  > 
A custom suggestions list component to use instead of the default.
</PropertyReference>

<PropertyReference name="Input" type="React.ComponentType<InputProps>"  > 
A custom Input component to use instead of the default.
</PropertyReference>

<PropertyReference name="className" type="string"  > 
A class name to apply to the root element.
</PropertyReference>

<PropertyReference name="children" type="React.ReactNode"  > 
Children to render.
</PropertyReference>

<PropertyReference name="defaultOpen" type="boolean"  default="false"> 
Whether the chat window should be open by default.
</PropertyReference>

<PropertyReference name="clickOutsideToClose" type="boolean"  default="true"> 
If the chat window should close when the user clicks outside of it.
</PropertyReference>

<PropertyReference name="hitEscapeToClose" type="boolean"  default="true"> 
If the chat window should close when the user hits the Escape key.
</PropertyReference>

<PropertyReference name="shortcut" type="string"  default="'/'"> 
The shortcut key to open the chat window.
  Uses Command-[shortcut] on a Mac and Ctrl-[shortcut] on Windows.
</PropertyReference>

<PropertyReference name="onSetOpen" type="(open: boolean) => void"  > 
A callback that gets called when the chat window opens or closes.
</PropertyReference>

<PropertyReference name="Window" type="React.ComponentType<WindowProps>"  > 
A custom Window component to use instead of the default.
</PropertyReference>

<PropertyReference name="Button" type="React.ComponentType<ButtonProps>"  > 
A custom Button component to use instead of the default.
</PropertyReference>

<PropertyReference name="Header" type="React.ComponentType<HeaderProps>"  > 
A custom Header component to use instead of the default.
</PropertyReference>

