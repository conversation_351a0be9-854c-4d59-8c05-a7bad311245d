---
title: "GoogleGenerativeAIAdapter"
description: "Copilot Runtime adapter for Google Generative AI (e.g. Gemini)."
---

{
 /*
  * ATTENTION! DO NOT MODIFY THIS FILE!
  * This page is auto-generated. If you want to make any changes to this page, changes must be made at:
  * CopilotKit/packages/runtime/src/service-adapters/google/google-genai-adapter.ts
  */
}
Copilot Runtime adapter for Google Generative AI (e.g. Gemini).
 
## Example
 
```ts
import { CopilotRuntime, GoogleGenerativeAIAdapter } from "@copilotkit/runtime";
const { GoogleGenerativeAI } = require("@google/generative-ai");
 
const genAI = new GoogleGenerativeAI(process.env["GOOGLE_API_KEY"]);
 
const copilotKit = new CopilotRuntime();
 
return new GoogleGenerativeAIAdapter({ model: "gemini-1.5-pro" });
```

## Constructor Parameters

<PropertyReference name="model" type="string"  > 
A custom Google Generative AI model to use.
</PropertyReference>

