---
title: "AnthropicAdapter"
description: "Copilot Runtime adapter for Anthropic."
---

{
 /*
  * ATTENTION! DO NOT MODIFY THIS FILE!
  * This page is auto-generated. If you want to make any changes to this page, changes must be made at:
  * CopilotKit/packages/runtime/src/service-adapters/anthropic/anthropic-adapter.ts
  */
}
Copilot Runtime adapter for Anthropic.
 
## Example
 
```ts
import { CopilotRuntime, AnthropicAdapter } from "@copilotkit/runtime";
import Anthropic from "@anthropic-ai/sdk";
 
const copilotKit = new CopilotRuntime();
 
const anthropic = new Anthropic({
  apiKey: "<your-api-key>",
});
 
return new AnthropicAdapter({ anthropic });
```

## Constructor Parameters

<PropertyReference name="anthropic" type="Anthropic"  > 
An optional Anthropic instance to use.  If not provided, a new instance will be
  created.
</PropertyReference>

<PropertyReference name="model" type="string"  > 
The model to use.
</PropertyReference>

