---
title: "LangChainAdapter"
description: "Copilot Runtime adapter for LangChain."
---

{
 /*
  * ATTENTION! DO NOT MODIFY THIS FILE!
  * This page is auto-generated. If you want to make any changes to this page, changes must be made at:
  * CopilotKit/packages/runtime/src/service-adapters/langchain/langchain-adapter.ts
  */
}
Copilot Runtime adapter for LangChain.
 
## Example
 
```ts
import { CopilotRuntime, LangChainAdapter } from "@copilotkit/runtime";
import { ChatOpenAI } from "@langchain/openai";
 
const copilotKit = new CopilotRuntime();
 
const model = new ChatOpenAI({
  model: "gpt-4o",
  apiKey: "<your-api-key>",
});
 
return new LangChainAdapter({
  chainFn: async ({ messages, tools }) => {
    return model.bindTools(tools).stream(messages);
    // or optionally enable strict mode
    // return model.bindTools(tools, { strict: true }).stream(messages);
  }
});
```
 
The asynchronous handler function (`chainFn`) can return any of the following:
 
- A simple `string` response
- A LangChain stream (`IterableReadableStream`)
- A LangChain `BaseMessageChunk` object
- A LangChain `AIMessage` object

## Constructor Parameters

<PropertyReference name="chainFn" type="(parameters: ChainFnParameters) => Promise<LangChainReturnType>" required > 
A function that uses the LangChain API to generate a response.
</PropertyReference>

