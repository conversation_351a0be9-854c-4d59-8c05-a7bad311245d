---
title: "useLangGraphInterrupt"
description: "The useLangGraphInterrupt hook allows setting the generative UI to be displayed on LangGraph's Interrupt event."
---

<br />
<video src="/images/coagents/interrupt-flow.mp4" className="rounded-lg shadow-xl" loop playsInline controls autoPlay muted />

`useLangGraphInterrupt` is a React hook that you can use in your application to provide
custom UI to be rendered when using `interrupt` by LangGraph.
Once an Interrupt event is emitted, that hook would execute, allowing to receive user input with a user experience to your choice.

## Usage

### Simple Usage

```tsx title="app/page.tsx"
import { useLangGraphInterrupt } from "@copilotkit/react-core"; // [!code highlight]
// ...

const YourMainContent = () => {
  // ...
  // [!code highlight:16]
  // styles omitted for brevity
  useLangGraphInterrupt<string>({
    render: ({ event, resolve }) => (
      <div>
        <p>{event.value}</p>
        <form onSubmit={(e) => {
          e.preventDefault();
          resolve((e.target as HTMLFormElement).response.value);
        }}>
          <input type="text" name="response" placeholder="Enter your response" />
          <button type="submit">Submit</button>
        </form>
      </div>
    )
  });
  // ...

  return <div>{/* ... */}</div>
}
```

## Parameters

<PropertyReference name="action" type="Action" required>
  The action to perform when an Interrupt event is emitted. Either `handler` or `render` must be defined as arguments

    <PropertyReference name="name" type="string" required>
      The name of the action.
    </PropertyReference>

    <PropertyReference name="handler" type="(args: LangGraphInterruptRenderProps<T>) => any | Promise<any>">
      A handler to programmatically resolve the Interrupt, or perform operations which result will be passed to the `render` method
    </PropertyReference>

    <PropertyReference name="render" type="(props: LangGraphInterruptRenderProps<T>) => string | React.ReactElement">
        Render lets you define a custom component or string to render when an Interrupt event is emitted.
    </PropertyReference>

    <PropertyReference name="enabled" type="(args: { eventValue: TEventValue; agentMetadata: AgentSession }) => boolean">
        Method that returns a boolean, indicating if the interrupt action should run. Useful when using multiple interrupts
    </PropertyReference>
</PropertyReference>

<PropertyReference name="dependencies" type="any[]">
  An optional array of dependencies.
</PropertyReference>
