---
title: "useCopilotReadable"
description: "The useCopilotReadable hook allows you to provide knowledge to your copilot (e.g. application state)."
---

{
 /*
  * ATTENTION! DO NOT MODIFY THIS FILE!
  * This page is auto-generated. If you want to make any changes to this page, changes must be made at:
  * CopilotKit/packages/react-core/src/hooks/use-copilot-readable.ts
  */
}
`useCopilotReadable` is a React hook that provides app-state and other information
to the Copilot. Optionally, the hook can also handle hierarchical state within your
application, passing these parent-child relationships to the Copilot.
 
## Usage
 
### Simple Usage
 
In its most basic usage, useCopilotReadable accepts a single string argument
representing any piece of app state, making it available for the Copilot to use
as context when responding to user input.
 
```tsx
import { useCopilotReadable } from "@copilotkit/react-core";
 
export function MyComponent() {
  const [employees, setEmployees] = useState([]);
 
  useCopilotReadable({
    description: "The list of employees",
    value: employees,
  });
}
```
 
### Nested Components
 
Optionally, you can maintain the hierarchical structure of information by passing
`parentId`. This allows you to use `useCopilotReadable` in nested components:
 
```tsx /employeeContextId/1 {17,23}
import { useCopilotReadable } from "@copilotkit/react-core";
 
function Employee(props: EmployeeProps) {
  const { employeeName, workProfile, metadata } = props;
 
  // propagate any information to copilot
  const employeeContextId = useCopilotReadable({
    description: "Employee name",
    value: employeeName
  });
 
  // Pass a parentID to maintain a hierarchical structure.
  // Especially useful with child React components, list elements, etc.
  useCopilotReadable({
    description: "Work profile",
    value: workProfile.description(),
    parentId: employeeContextId
  });
 
  useCopilotReadable({
    description: "Employee metadata",
    value: metadata.description(),
    parentId: employeeContextId
  });
 
  return (
    // Render as usual...
  );
}
```

## Parameters

<PropertyReference name="description" type="string" required > 
The description of the information to be added to the Copilot context.
</PropertyReference>

<PropertyReference name="value" type="any" required > 
The value to be added to the Copilot context. Object values are automatically stringified.
</PropertyReference>

<PropertyReference name="parentId" type="string"  > 
The ID of the parent context, if any.
</PropertyReference>

<PropertyReference name="categories" type="string[]"  > 
An array of categories to control which context are visible where. Particularly useful
  with CopilotTextarea (see `useMakeAutosuggestionFunction`)
</PropertyReference>

<PropertyReference name="available" type="'enabled' | 'disabled'"  > 
Whether the context is available to the Copilot.
</PropertyReference>

<PropertyReference name="convert" type="(description: string, value: any) => string"  > 
A custom conversion function to use to serialize the value to a string. If not provided, the value
  will be serialized using `JSON.stringify`.
</PropertyReference>

