---
title: "useCoAgent"
description: "The useCoAgent hook allows you to share state bidirectionally between your application and the agent."
---

{
 /*
  * ATTENTION! DO NOT MODIFY THIS FILE!
  * This page is auto-generated. If you want to make any changes to this page, changes must be made at:
  * CopilotKit/packages/react-core/src/hooks/use-coagent.ts
  */
}
<Callout type="info">
  Usage of this hook assumes some additional setup in your application, for more information
  on that see the CoAgents <span className="text-blue-500">[getting started guide](/coagents/quickstart/langgraph)</span>.
</Callout>
<Frame className="my-12">
  <img
    src="/images/coagents/SharedStateCoAgents.gif"
    alt="CoAgents demonstration"
    className="w-auto"
  />
</Frame>
 
This hook is used to integrate an agent into your application. With its use, you can
render and update the state of an agent, allowing for a dynamic and interactive experience.
We call these shared state experiences agentic copilots, or CoAgents for short.
 
## Usage
 
### Simple Usage
 
```tsx
import { useCoAgent } from "@copilotkit/react-core";
 
type AgentState = {
  count: number;
}
 
const agent = useCoAgent<AgentState>({
  name: "my-agent",
  initialState: {
    count: 0,
  },
});
 
```
 
`useCoAgent` returns an object with the following properties:
 
```tsx
const {
  name,     // The name of the agent currently being used.
  nodeName, // The name of the current LangGraph node.
  state,    // The current state of the agent.
  setState, // A function to update the state of the agent.
  running,  // A boolean indicating if the agent is currently running.
  start,    // A function to start the agent.
  stop,     // A function to stop the agent.
  run,      // A function to re-run the agent. Takes a HintFunction to inform the agent why it is being re-run.
} = agent;
```
 
Finally we can leverage these properties to create reactive experiences with the agent!
 
```tsx
const { state, setState } = useCoAgent<AgentState>({
  name: "my-agent",
  initialState: {
    count: 0,
  },
});
 
return (
  <div>
    <p>Count: {state.count}</p>
    <button onClick={() => setState({ count: state.count + 1 })}>Increment</button>
  </div>
);
```
 
This reactivity is bidirectional, meaning that changes to the state from the agent will be reflected in the UI and vice versa.
 
## Parameters
<PropertyReference name="options" type="UseCoagentOptions<T>" required>
  The options to use when creating the coagent.
  <PropertyReference name="name" type="string" required>
    The name of the agent to use.
  </PropertyReference>
  <PropertyReference name="initialState" type="T | any">
    The initial state of the agent.
  </PropertyReference>
  <PropertyReference name="state" type="T | any">
    State to manage externally if you are using this hook with external state management.
  </PropertyReference>
  <PropertyReference name="setState" type="(newState: T | ((prevState: T | undefined) => T)) => void">
    A function to update the state of the agent if you are using this hook with external state management.
  </PropertyReference>
</PropertyReference>

