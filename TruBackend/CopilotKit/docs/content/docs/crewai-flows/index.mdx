---
title: Introduction
icon: "lucide/Sparkles"
description: Build Agent-Native Applications (ANAs) powered by CopilotKit and CrewAI Flows.
---

import { BiSolidMessage as TextIcon } from "react-icons/bi";
import { Vsc<PERSON>son as JsonIcon } from "react-icons/vsc";
import { FaDiscord } from "react-icons/fa";
import Link from "next/link";
import { YouTubeVideo } from "@/components/react/youtube-video";
import { CoAgentsEnterpriseCTA } from "@/components/react/coagents/coagents-enterprise-cta.tsx";
import {
  CoAgentsFeatureToggle,
  CoAgentsFeatureRender,
} from "@/components/react/coagents/coagents-features.tsx";
import { TelescopeIcon } from "lucide-react";
import { DynamicContentWrapper } from "@/components/react/dynamic-content-wrapper";
import { ExamplesCarousel } from "@/components/react/examples-carousel";
import {
  LuPlane,
  LuBookOpen,
  LuLightbulb,
  LuLayoutTemplate,
  LuBrainCog,
  <PERSON><PERSON>ser<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  LuMessageS<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "react-icons/lu";
import { CoAgentsExamples } from "@/components/react/examples-carousel";
import { CTACards } from "@/components/react/cta-cards";
import { FaSync } from "react-icons/fa";
import { Socials } from "@/components/react/socials";

<div className="p-4 mb-6 rounded-lg bg-orange-50 dark:bg-orange-950 border border-orange-200 dark:border-orange-800">
  <div className="flex items-center gap-2 mb-2">
    <TelescopeIcon className="h-5 w-5 text-orange-500 dark:text-orange-300" />
    <h3 className="text-lg font-semibold text-orange-700 dark:text-orange-300">CrewAI Overview</h3>
  </div>
  <p className="text-orange-700 dark:text-orange-300">
    Visit the <a href="https://v0-crew-land.vercel.app/" target="_blank" rel="noopener noreferrer" className="font-medium underline underline-offset-4 decoration-orange-400 dark:decoration-orange-500 hover:text-orange-600 dark:hover:text-orange-200">CrewAI Overview Page</a> to learn more about CrewAI's capabilities and features.
  </p>
</div>

# Copilot Infrastructure for CrewAI Flows

Full user-interaction infrastructure for your Flows, to turn them into Copilot Agents (CoAgents).

<Frame className="mt-0 mb-6">
  <video
    src="/images/coagents/crew-ai/flows/crews-interaction-layer.mp4"
    alt="CoAgents demonstration"
    className="rounded-lg shadow-xl"
    playsInline
    autoPlay
    muted
    loop
  />
</Frame>

## Building blocks of a CoAgent

Everything you need to build Agent-Native Applications (ANAs), right out of the box.

<Cards className="gap-6">
  <Card
    className="p-6 rounded-xl text-base"
    icon={<LuMessageSquare className="text-orange-500 dark:text-orange-300" />}
    title="Agentic Chat UI"
    description="In-app chat to kickoff, render and analyze the result of your Crews."
    href="/crewai-flows/agentic-chat-ui"
  />
    <Card
    className="p-6 rounded-xl text-base"
    icon={<FaSync className="text-orange-500 dark:text-orange-300" />}
    title="Shared State"
    description="Your agent can see/update everything in your app, and vice versa."
    href="/crewai-flows/shared-state"
  />
  <Card
    className="p-6 rounded-xl text-base"
    icon={<LuLayoutTemplate className="text-orange-500 dark:text-orange-300" />}
    title="Generative UI"
    description="UI that updates in real-time based on your agent's state."
    href="/crewai-flows/generative-ui"
  />
  <Card
    className="p-6 rounded-xl text-base"
    icon={<LuWand2 className="text-orange-500 dark:text-orange-300" />}
    title="Frontend Tools"
    description="Give your agent the ability to take action in your application."
    href="/crewai-flows/frontend-actions"
  />
  <Card
    className="p-6 rounded-xl text-base"
    icon={<LuWand2 className="text-orange-500 dark:text-orange-300" />}
    title="Multi-Agent Coordination"
    description="Route your agent to the right agent based on the user's request."
    href="/crewai-flows/multi-agent-flows"
  />
  <Card
    className="p-6 rounded-xl text-base"
    icon={<LuUserCog className="text-orange-500 dark:text-orange-300" />}
    title="Human-in-the-Loop"
    description="Set smart checkpoints where humans can guide your agents."
    href="/crewai-flows/human-in-the-loop"
  />
</Cards>

## Ready to get started?

Get started with CoAgents in as little as 5 minutes with one of our guides or tutorials.

<Cards className="gap-6">
  <Card
    className="p-6 rounded-xl text-base"
    icon={<LuPlay className="text-orange-500 dark:text-orange-300" />}
    title="Quickstart"
    description="Learn how to build your first CoAgent in 10 minutes."
    href="/crewai-flows/quickstart/crewai"
  />
  <Card
    className="p-6 rounded-xl text-base"
    icon={<TelescopeIcon className="text-orange-500 dark:text-orange-300" />}
    title="Feature Overview"
    description="Try the key features of CoAgents powered by CrewAI Crews."
    href="https://demo-viewer-five.vercel.app/"
    target="_blank"
  />
</Cards>

## Common Questions

Have a question about CoAgents? You're in the right place!

<Accordions>
<Accordion title="Can you explain what a CoAgent is in more detail?">
Sure! CoAgents are what we call "agentic copilots". Well, what's an agentic copilot then?

Think of a Copilot as a simple and fully LLM controlled assistant that has relatively limited capabilities. An Agentic Copilot then is a copilot
that has been enhanced with the ability to use CrewAI agents to perform more complex tasks. This is an extremely powerful way to build AI
powered applications because it gives you, the developer, the ability to control the agent's behavior in a deterministic way while still letting
the agent do its magic.

For more on this topic, checkout our [agentic copilot](/crewai-flows/concepts/agentic-copilots) concept page.

</Accordion>
</Accordions>
