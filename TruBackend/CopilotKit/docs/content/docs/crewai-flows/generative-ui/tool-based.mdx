---
title: Tool-based Generative UI
icon: "lucide/Wrench"
description: Render your agent's tool calls with custom UI components.
---

import { Accordions, Accordion } from "fumadocs-ui/components/accordion";
import RunAndConnectAgentSnippet from "@/snippets/coagents/run-and-connect-agent.mdx";

<video
  src="/images/coagents/tool-based-gen-ui.mp4"
  className="rounded-lg shadow-xl"
  loop
  playsInline
  controls
  autoPlay
  muted
/>
<Callout>
  This video demonstrates the [implementation](#implementation) section applied
  to out [coagents starter
  project](https://github.com/CopilotKit/CopilotKit/tree/main/examples/coagents-starter-crewai-flows).
</Callout>

## What is this?

Tools are a way for the LLM to call predefined, typically, deterministic functions. CopilotKit allows you to render these tools in the UI
as a custom component, which we call **Generative UI**.

## When should I use this?

Rendering tools in the UI is useful when you want to provide the user with feedback about what your agent is doing, specifically
when your agent is calling tools. CopilotKit allows you to fully customize how these tools are rendered in the chat.

## Implementation

<Steps>
<Step>
### Run and connect your agent

You'll need to run your agent and connect it to CopilotKit before proceeding. If you haven't done so already,
you can follow the instructions in the [Getting Started](/crewai-flows/quickstart/crewai) guide.

If you don't already have an agent, you can use the [coagent starter](https://github.com/CopilotKit/CopilotKit/tree/main/examples/coagents-starter-crewai-flows) as a starting point
as this guide uses it as a starting point.

</Step>
<Step>
### Give your agent a tool to call

<Tabs groupId="language" items={['Python']} default="Python">
    <Tab value="Python">
        ```python title="agent/sample_agent/agent.py"
        from crewai.flow.flow import start
        from litellm import completion
        # ...

        # [!code highlight:17]
        WEATHER_TOOL = {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get the weather for a given location.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The location to get weather for"
                        }
                    },
                    "required": ["location"]
                }
            }
        }

        # ...

        @start
        async def chat(self):

            response = await copilotkit_stream(
                completion(
                    model="openai/gpt-4o",
                    messages=[
                        {"role": "system", "content": prompt},
                        *self.state.get("messages", [])
                    ],
                    tools=[WEATHER_TOOL],
                    stream=True
                )
            )

            # ...
        ```
    </Tab>

</Tabs>
</Step>
<Step>
### Render the tool call in your frontend
At this point, your agent will be able to call the `get_weather` tool. Now 
we just need to add a `useCopilotAction` hook to render the tool call in
the UI.

<Callout type="info" title="Important">
  In order to render a tool call in the UI, the name of the action must match
  the name of the tool.
</Callout>

```tsx title="app/page.tsx"
import { useCopilotAction } from "@copilotkit/react-core"; // [!code highlight]
// ...

const YourMainContent = () => {
  // ...
  // [!code highlight:13]
  useCopilotAction({
    name: "get_weather",
    available: "disabled", // Don't allow the agent or UI to call this tool as its only for rendering
    render: ({ status, args }) => {
      return (
        <p className="text-gray-500 mt-2">
          {status !== "complete" && "Calling weather API..."}
          {status === "complete" &&
            `Called the weather API for ${args.location}.`}
        </p>
      );
    },
  });
  // ...
};
```

</Step>
<Step>
### Give it a try!

Try asking the agent to get the weather for a location. You should see the custom UI component that we added
render the tool call and display the arguments that were passed to the tool.

</Step>
</Steps>
