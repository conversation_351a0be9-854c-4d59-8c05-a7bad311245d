---
title: Introduction
description: "Build production-ready Copilots and Agents effortlessly."
icon: "lucide/Sparkles"
---
import { ImageZoom } from 'fumadocs-ui/components/image-zoom';
import { ExamplesCarousel } from "@/components/react/examples-carousel";
import { CTACards } from "@/components/react/cta-cards";
import { YouTubeVideo } from "@/components/react/youtube-video";

import { MdMessage } from "react-icons/md";
import { TbSparkles } from "react-icons/tb";
import { SiLangchain } from "react-icons/si";
import { AG2Icon } from "@/lib/icons/custom-icons";
import { <PERSON><PERSON><PERSON><PERSON> } from "@icons-pack/react-simple-icons";
import { FileSpreadsheet, Banknote, Plane, BookOpen, Telescope, Play } from "lucide-react";

# What is CopilotKit?

At its core, CopilotKit is a set of tools that make it easy to **let your users work
alongside Large Language Models (LLMs) to accomplish generative tasks** directly in
your application. Instead of just using the LLM to generate content, you can let it
take direct action alongside your users.

Interacting with these models can be done directly (**Standard**) or through agents (**CoAgents**).

## Standard
Utilize CopilotKit's standard agentic runloop to get started quickly.

<Cards className="gap-6">
  <Card
    className="p-6 rounded-xl text-base"
    title="Quickstart"
    description="Get started with CopilotKit directly in your application."
    href="/quickstart"
    icon={<Play className="text-indigo-500 dark:text-indigo-300" />}
  />
  <Card 
    className="p-6 rounded-xl text-base"
    title="Tutorial"
    description="Build an AI todo app with CopilotKit in minutes."
    href="/tutorials/ai-todo-app/overview"
    icon={<BookOpen className="text-indigo-500 dark:text-indigo-300" />}
  />
</Cards>

## CoAgents
When you need **complete control** over the agentic runloop, you can use **CoAgents**. Bridge the remaining gap between demos and production-ready experiences.

<Cards className="gap-6">
  <Card
    className="p-6 rounded-xl text-base"
    title="LangGraph"
    description="User-interactive agents with LangGraph."
    href="/coagents"
    icon={<SiLangchain className="text-indigo-500 dark:text-indigo-300" />}
  />
  <Card 
    className="p-6 rounded-xl text-base"
    title="CrewAI Crews"
    description="Build multi-agent workflows with CrewAI."
    href="/crewai-crews"
    icon={<SiCrewai className="text-indigo-500 dark:text-indigo-300" />}
  />
  <Card 
    className="p-6 rounded-xl text-base"
    title="CrewAI Flows"
    description="User-interactive agents with CrewAI."
    href="/crewai-flows"
    icon={<SiCrewai className="text-indigo-500 dark:text-indigo-300" />}
  />
  <Card 
    className="p-6 rounded-xl text-base"
    title="AG2"
    description="Autonomous, collaborative AI agents with AG2."
    href="/ag2"
    icon={<AG2Icon className="text-indigo-500 dark:text-indigo-300 h-6 w-6" />}
  />
</Cards>

## CopilotKit in Action
Need some inspiration? Check out somethings we've built with CopilotKit.

<Cards className="gap-6">
  <Card
    className="md:col-span-2 p-6 py-10 rounded-xl text-base" 
    title="Feature Viewer"
    description="Learn about all of the best features CopilotKit has to offer with an interactive experience."
    href="https://feature-viewer-langgraph.vercel.app/"
  />
  <Card 
    className="p-6 rounded-xl text-base"
    title="Spreadsheet Copilot"
    description="A powerful spreadsheet assistant that helps users analyze data, create formulas, and generate insights."
    href="https://spreadsheet-demo-tau.vercel.app/"
    icon={<FileSpreadsheet className="text-indigo-500 dark:text-indigo-300" />}
  />
  <Card 
    className="p-6 rounded-xl text-base"
    title="SaaS Copilot"
    description="An AI-powered banking interface that helps users understand and interact with their finances."
    href="https://brex-demo-temp.vercel.app/"
    icon={<Banknote className="text-indigo-500 dark:text-indigo-300" />}
  />
  <Card 
    className="p-6 rounded-xl text-base"
    title="Agent-Native Travel Planner"
    description="Interactive travel planning assistant that helps users generate and build travel itineraries."
    href="https://examples-coagents-ai-travel-app.vercel.app/"
    icon={<Plane className="text-indigo-500 dark:text-indigo-300" />}
  />
  <Card 
    className="p-6 rounded-xl text-base"
    title="Agent-Native Research Canvas"
    description="An intelligent research assistant that helps users synthesize information across multiple sources."
    href="https://examples-coagents-research-canvas-ui.vercel.app/"
    icon={<BookOpen className="text-indigo-500 dark:text-indigo-300" />}
  />
</Cards>

## How does CopilotKit work?
CopilotKit is thoughtfully architected to scale with you, your teams, and your product.
<ImageZoom src="/images/architecture-diagram.png" className="rounded-2xl" width={1000} height={1000}/>

## Common Questions
We've got answers to some common questions!

<Accordions>
  <Accordion title="What is a Copilot?">
    A Copilot is a trusted partner that lives in your application to help your users get things done.
    There are two main types, the **Concierge** and the **Worker**.

    ### Concierge

    The Concierge Copilot understands your application's capabilities and full user context. It translates 
    high-level user intent into actions by serving as an intelligent intermediary.

    For example, our [Banking Assistant](https://github.com/CopilotKit/demo-banking) implements Concierge Copilots 
    to help users manage their (_fake_) banking needs.
    

    ### Worker

    The Worker Copilot is a domain-specific agent that can help users perform their core work tasks.
    It serves as a partner to the user that is better at performing some tasks and worse at others. 
    Ultimately, it amplifies your users to produce better work than they thought possible. This pattern
    is often used in backoffice copilots.

    Think of the Worker Copilot as Cursor, Replit Agent, or Windsurf, but for any domain. For example, 
    see our [Open Researcher ANA](https://github.com/CopilotKit/open-research-ana).
  </Accordion>
  <Accordion title="What are the main features of CopilotKit?">
    ### Batteries included chat components
    Beautiful, powerful and customizable chat components just an import away.
    | | |
    |---------|-------------|
    | [**Chat**](/reference/components/chat/CopilotChat) | Simple and powerful chat interface |
    | [**Pop-up**](/reference/components/chat/CopilotPopup) | The Chat component in a pop-up format |
    | [**Sidebar**](/reference/components/chat/CopilotSidebar) | The Chat component in a sidebar format |
    | [**Copilot Textarea**](/reference/components/CopilotTextarea) | Powerful AI autocompletion as a drop-in replacement for any textarea |
    | [**Headless**](/guides/custom-look-and-feel/customize-built-in-ui-components) | Full customization of the chat interfaces |

    ### Deeply integrated Copilots
    Give Copilots the ability to take actions directly in your application.
    |  |  |
    |---------|-------------|
    | [**Copilot Readable State**](/reference/hooks/useCopilotReadable) | Enables Copilots to read and understand the application state |
    | [**Copilot Actions**](/reference/hooks/useCopilotAction) | Copilots can perform actions in the application |
    | [**Generative UI**](/guides/generative-ui) | Render any component in the copilot chat interface |
    | [**AI Autosuggestions**](/reference/hooks/useCopilotChatSuggestions) | AI-powered autosuggestions in your AI chat interface |
    | [**Copilot Tasks**](/reference/classes/CopilotTask) | Let your copilots take actions proactively based on application state |

    ### Rich agentic experiences
    Integrate your LangGraph agents into your product with ease.
    | |  |
    |---------|-------------|
    | [**Deep support for LangGraph**](/coagents/quickstart/langgraph) | Bring your LangGraph agents directly into your product |
    | [**Human-in-the-loop**](/coagents/human-in-the-loop) | Allow your users to work with your agents to solve complex problems |
    | [**Shared state**](/coagents/shared-state) | Render the state of your LangGraph agents with less than 10 lines of code |
    | [**Multi-agent**](/coagents/multi-agent-flows) | Build multi-agent experiences with ease |
  </Accordion>
  <Accordion title="How does it all work?">
    Great question! CopilotKit has three main components:
    1. **CopilotKit UI**: The UI components that you use to build your Copilots and Agents.
    2. **CopilotKit Runtime**: The runtime that you use to build your Copilots and Agents. This serves as the backend for your Copilots and Agents.
    3. **CopilotKit SDK**: In more complex applications, you'll use the SDK to deeply integrate CopilotKit into your agents.
  </Accordion>

  <Accordion title="Can I use any LLM with CopilotKit?">
    Yes! CopilotKit supports most LLMs, including OpenAI, Anthropic, Google, and more. In addition, you can use any LLM that is supported by LangGraph or
    LangChain.

    For more information, checkout our documentation on [bringing your own LLM](/guides/bring-your-own-llm).
  </Accordion>
</Accordions>
