---
title: "Connecting Your Data"
description: "Learn how to connect your data to CopilotKit."
icon: "lucide/Database"
---
import { CodeIcon, ServerIcon } from "lucide-react";

CopilotKit allows you to connect your data through the frontend and through the backend. This enables
a variety of use-cases from simple context to RAG-based LLM interactions.

<Cards>
  <Card
    title="Frontend Data"
    icon={<CodeIcon />}
    description="Learn how to connect your data to CopilotKit on the frontend."
    href="/guides/connect-your-data/frontend"
  />
  <Card
    title="Backend Data"
    icon={<ServerIcon />}
    description="Learn how to connect your data to CopilotKit on the backend."
    href="/guides/connect-your-data/backend" />
</Cards>