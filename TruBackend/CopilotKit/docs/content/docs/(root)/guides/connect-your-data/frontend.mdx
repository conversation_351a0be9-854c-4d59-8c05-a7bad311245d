---
title: "Frontend Data"
description: "Learn how to connect your data to CopilotKit."
icon: "lucide/CodeXml"
---
import UseClientCalloutSnippet from "@/snippets/use-client-callout.mdx";

For your copilot to best answer your users' needs, you will want to provide it with **context-specific**, **user-specific**, and oftentimes **realtime** data. CopilotKit makes it easy to do so.

<Steps>
  <Step>
    ### Add the data to the Copilot

    The [`useCopilotReadable` hook](/reference/hooks/useCopilotReadable) is used to add data as context to the Copilot.

    ```tsx title="YourComponent.tsx" showLineNumbers {1, 7-10}
    "use client" // only necessary if you are using Next.js with the App Router. // [!code highlight]
    import { useCopilotReadable } from "@copilotkit/react-core"; // [!code highlight]
    import { useState } from 'react';
    
    export function YourComponent() {
      // Create colleagues state with some sample data
      const [colleagues, setColleagues] = useState([
        { id: 1, name: "<PERSON>", role: "Developer" },
        { id: 2, name: "<PERSON>", role: "<PERSON>" },
        { id: 3, name: "<PERSON>", role: "Product Manager" }
      ]);
    
      // Define Copilot readable state
      // [!code highlight:5]
      useCopilotReadable({
        description: "The current user's colleagues",
        value: colleagues,
      });
      return (
        // Your custom UI component
        <>...</>
      );
    }
    ```
  </Step>

  <Step>
    <UseClientCalloutSnippet components={props.components} />
  </Step>


  <Step>
    ### Test it out!

    The data you provided is now available to the Copilot.
    Test it out by passing some data in the hook and asking the copilot questions about it.

    <div className="mt-2 mb-4">
      <img 
        src="/images/connect-your-data-example.gif" 
        alt="Example of connecting data to Copilot" 
        className="rounded-lg shadow-md w-full max-w-md mx-auto"
      />
    </div>
  </Step>
</Steps>
