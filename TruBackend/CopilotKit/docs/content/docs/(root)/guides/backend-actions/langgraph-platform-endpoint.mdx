---
title: "Remote Endpoint (LangGraph Platform)"
description: "Connect your CopilotKit application to an agent deployed on LangGraph Platform."
icon: "custom/langchain"
---
import FindCopilotRuntimeSnippet from "@/snippets/find-your-copilot-runtime.mdx";
import { Accordions, Accordion } from "fumadocs-ui/components/accordion";
import { TailoredContent, TailoredContentOption } from "@/components/react/tailored-content.tsx";
import CopilotCloudConfigureRemoteEndpointLangGraphSnippet from "@/snippets/copilot-cloud-configure-remote-endpoint-langgraph.mdx";
import CopilotCloudConfigureCopilotkitProviderSnippet from "@/snippets/copilot-cloud-configure-copilotkit-provider.mdx";
import LangGraphPlatformDeploymentTabs from "@/snippets/langgraph-platform-deployment-tabs.mdx";
import { FaCloud, FaServer } from "react-icons/fa";

<Callout type="info">
    This guide assumes you've created a LangGraph agent, and have a `langgraph.json` file set up. If you need a quick introduction, check out [this brief example
    from the LangGraph docs](https://langchain-ai.github.io/langgraph/) or follow one of our demos.
</Callout>
## Deploy a Graph to LangGraph Platform

<Steps>

    <Step>
    ### Deploy your agent

    First, you need to host your agent so that CopilotKit can access it.

    <LangGraphPlatformDeploymentTabs components={props.components} />
    </Step>

    <Step>
    ### Setup your Copilot Runtime

    <TailoredContent id="hosting">
        <TailoredContentOption
            id="copilot-cloud"
            title="Copilot Cloud (Recommended)"
            description="I'm already using or want to use Copilot Cloud."
            icon={<FaCloud />}
        >
            If you followed the [Copilot Cloud Quickstart](/docs/quickstart) and opted to use CopilotCloud,
            you only need to add your LangGraph Platform deployment URL and LangSmith API key to your CopilotCloud.

            <Accordions className="my-4">
                <Accordion title="Haven't setup Copilot Cloud yet? Click here!" >
                    Copilot Cloud hooks into your CopilotKit provider, so you'll need to set it up first if you haven't already.
                    <CopilotCloudConfigureCopilotkitProviderSnippet components={props.components} />
                </Accordion>
            </Accordions>
            <CopilotCloudConfigureRemoteEndpointLangGraphSnippet components={props.components} />
        </TailoredContentOption>
        <TailoredContentOption
            id="self-hosted"
            title="Self-Hosted"
            description="I'm using or want to use a self-hosted Copilot Runtime."
            icon={<FaServer />}
        >
            <Steps>
                <Step>
                    <FindCopilotRuntimeSnippet components={props.components} />
                </Step>
                <Step>
                    Update the `CopilotRuntime` config to include the `remoteEndpoints` property:

                    ```tsx
                    const runtime = new CopilotRuntime({
                        // ...existing configuration
                        remoteEndpoints: [ // [!code highlight:9]
                            langGraphPlatformEndpoint({
                                deploymentUrl: "your-api-url",
                                langsmithApiKey: "your-langsmith-api-key",
                                // List of all agents which are available under "graphs" list in your langgraph.json file.
                                agents: [{ name: 'my_agent', description: 'A helpful LLM agent', assistantId: 'ID-of-the-agent' }]
                            }),
                        ],
                    });
                    ```

                    <Callout type="info">
                        Tip: Use the `langGraphPlatformEndpoint` type constructor function
                    </Callout>
                </Step>
            </Steps>
        </TailoredContentOption>
    </TailoredContent>
    </Step>

    <Step>
        ### Test Your Implementation

        After setting up the remote endpoint and modifying your `CopilotRuntime`, you can test your implementation by asking the copilot to perform actions that invoke your agent.<br/>
        The graph and interactions can viewed in [LangGraph Studio](smith.langchain.com/studio) and any logs should be available on [LangSmith](smith.langchain.com)

    </Step>
</Steps>

---

## Troubleshooting

A few things to try if you are running into trouble:

1. Make sure that you listed your agents according to the graphs mentioned in the `langgraph.json` file
2. Make sure the agent names are the same between the agent Python implementation, the `langgraph.json` file and the remote endpoint declaration
3. Make sure the LangGraph Platform deployment has all environment variables listed as you need them to be, according to your agent implementation
