---
title: "Backend Actions & Agents"
description: "Learn how to enable backend actions & agents in your Copilot."
icon: "lucide/Server"
---

import { LinkIcon } from "lucide-react";

<Cards>
  <Card
    title="TypeScript / Node.js Actions"
    description="Implement backend actions using TypeScript or Node.js within the CopilotRuntime."
    href="/guides/backend-actions/typescript-backend-actions"
    icon={<LinkIcon />}
  />
  <Card
    title="LangChain.js Actions"
    description="Integrate LangChain JS chains as backend actions in your Copilot."
    href="/guides/backend-actions/langchain-js-backend-actions"
    icon={<LinkIcon />}
  />
  <Card
    title="LangServe Integration"
    description="Connect your Copilot to LangChain chains hosted as separate services."
    href="/guides/backend-actions/langserve-backend-actions"
    icon={<LinkIcon />}
  />
  <Card
    title="Python SDK"
    description="Use the CopilotKit Python SDK to create powerful remote actions and agents."
    href="/guides/backend-actions/remote-backend-endpoint"
    icon={<LinkIcon />}
  />
  <Card
    title="CoAgents (LangGraph)"
    description="Deeply embed LangGraph agents in applications"
    href="/coagents"
    icon={<LinkIcon />}
  />
</Cards>