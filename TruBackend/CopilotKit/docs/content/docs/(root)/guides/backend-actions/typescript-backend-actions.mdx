---
title: "TypeScript (Node.js)"
description: "Implement native backend actions using TypeScript or Node.js in CopilotKit."
icon: "custom/typescript"
---
import FindCopilotRuntimeSnippet from "@/snippets/find-your-copilot-runtime.mdx";

<Steps>

<Step>
<FindCopilotRuntimeSnippet />
</Step>

<Step>
### Modify CopilotRuntime to include TypeScript/Node.js actions

Once you've located your `CopilotRuntime`, you can add TypeScript/Node.js actions by modifying its configuration. Here's how to implement native backend actions:

<Callout>
**Note** that `actions` is not merely an array of actions, but an array **generator**.
This generator takes `properties` and `url` as input.

This means you can **customize which backend actions are made available** according to the current frontend URL, as well as custom properties you can pass from the frontend.
</Callout>

```tsx title="/api/copilotkit/route.ts"
const runtime = new CopilotRuntime({
  // ... existing configuration
  actions: ({properties, url}) => {
    // Note that actions returns not an array, but an array **generator**.
    // You can use the input parameters to the actions generator to expose different backend actions to the Copilot at different times: 
    // `url` is the current URL on the frontend application.
    // `properties` contains custom properties you can pass from the frontend application.
    
    return [
      {
        name: "fetchNameForUserId",
        description: "Fetches user name from the database for a given ID.",
        parameters: [
          {
            name: "userId",
            type: "string",
            description: "The ID of the user to fetch data for.",
            required: true,
          },
        ],
        handler: async ({userId}: {userId: string}) => {
          // do something with the userId
          // return the user data
          return {
            name: "Darth Doe",
          };
        },
      },
    ]
  }
});

// ... rest of your route definition
```

</Step>

<Step>
### Test your implementation

After adding the action, test it by asking the copilot to perform the task. Observe how it selects the correct task, executes it, and streams back relevant responses.
</Step>

</Steps>

## Key Points


- Each action is defined with a name, description, parameters, and a handler function.
- The handler function implements the actual logic of the action and can interact with your backend systems.

By using this method, you can create powerful, context-aware backend actions that integrate seamlessly with your CopilotKit application.