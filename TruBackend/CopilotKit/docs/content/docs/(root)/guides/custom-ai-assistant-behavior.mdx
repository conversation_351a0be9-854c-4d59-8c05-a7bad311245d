---
title: "Customize Instructions"
description: "Learn how to customize the behavior of your AI assistant."
icon: "lucide/NotepadText"
---

There are three main ways to customize the behavior of your AI assistant:
- [Appending to the prompt](#appending-to-the-prompt-recommended)
- [Passing the `instructions` parameter](#passing-the-instructions-parameter)
- [Overwriting the default `makeSystemMessage`](#overwriting-the-default-makesystemmessage-not-recommended)

## Appending to the prompt (Recommended)
CopilotKit provides the [useCopilotAdditionalInstructions](/reference/hooks/useCopilotAdditionalInstructions) hook which allows you to add content to the prompt with whatever
you want.

```tsx title="Home.tsx"
import { CopilotKit, useCopilotAdditionalInstructions } from "@copilotkit/react-core";
import { CopilotPopup } from "@copilotkit/react-ui"
 
function Chat() {
  useCopilotAdditionalInstructions({
    instructions: "Do not answer questions about the weather.",
  });
  return <CopilotPopup />
}

export function Home() {
  return (
    <CopilotKit>
      <Chat />
    </CopilotKit>
  )
}
```

You can even conditionally add instructions based on the application's state.

```tsx title="Home.tsx"
function Chat() {
  const [showWeather, setShowWeather] = useState(false);

  useCopilotAdditionalInstructions({
    instructions: "Do not answer questions about the weather.",
    available: showWeather ? "enabled" : "disabled"
  }, showWeather);
}
```

## Advanced

If appending to the prompt is not enough, you have some other options, specifically around updating the prompt directly.

### Passing the `instructions` parameter

The `instructions` parameter is the recommended way to customize AI assistant behavior. It will remain compatible with performance optimizations to the CopilotKit platform.

It can be customized for **Copilot UI** as well as **programmatically**:

<Tabs groupId="approach" items={['Copilot UI', 'Headless UI']}>
  <Tab value="Copilot UI">
    Copilot UI components accept an `instructions` property:

    ```tsx title="CustomCopilot.tsx"
    import { CopilotChat } from "@copilotkit/react-ui";

    <CopilotChat
      instructions="You are a helpful assistant specializing in tax preparation. Provide concise and accurate answers to tax-related questions." // [!code highlight]
      labels={{
        title: "Tax Preparation Assistant",
        initial: "How can I help you with your tax preparation today?",
      }}
    />
    ```

  </Tab>

  <Tab value="Headless UI">
    The `instructions` parameter can also be set programmatically via `setChatInstructions` method, coming from `useCopilotContext`, allowing for dynamic customization based on the application's state or user interactions.

    ```tsx title="Home.tsx"
    import { useEffect } from 'react';
    import { useCopilotContext } from "@copilotkit/react-core";

    const Home: React.FC = () => {
      // [!code highlight:6]
      const { setChatInstructions } = useCopilotContext();

      useEffect(() => {
        setChatInstructions("You are assisting the user as best as you can. Answer in the best way possible given the data you have.");
      }, [setChatInstructions]);

      return <>{/* Your components */}</>;
    };
    ```

  </Tab>
</Tabs>


### Overwriting the default system message

For cases requiring complete control over the system message, you can use the `makeSystemMessage` function. We highly recommend reading CopilotKit's default system message before deciding to overwrite it, which can be found [here](https://github.com/CopilotKit/CopilotKit/blob/e48a34a66bb4dfd210e93dc41eee7d0f22d1a0c4/CopilotKit/packages/react-core/src/hooks/use-copilot-chat.ts#L240-L258).

<Callout type="warn">
This approach is **not recommended** as it may interfere with more advanced optimizations made by CopilotKit. **Only use this approach if the other options are not enough.**
</Callout>

<Tabs groupId="approach" items={['Copilot UI', 'Headless UI']}>
  <Tab value="Copilot UI">
    ```tsx filename="CustomCopilot.tsx" showLineNumbers {10}
    import { CopilotChat } from "@copilotkit/react-ui";

    const CustomCopilot: React.FC = () => (
      <CopilotChat
        instructions="You are a knowledgeable tax preparation assistant. Provide accurate and concise answers to tax-related questions, guiding users through the tax filing process."
        labels={{
          title: "Tax Preparation Assistant",
          initial: "How can I assist you with your taxes today?",
        }}
        makeSystemMessage={myCustomTaxSystemMessage} // [!code highlight]
      />
    );
    ```

  </Tab>
  <Tab value="Headless UI">
    ```tsx filename="CustomCopilotHeadless.tsx" showLineNumbers {4, 6}
    import { useCopilotChat } from "@copilotkit/react-core";

    const CustomCopilotHeadless: React.FC = () => {
      // [!code highlight:5]
      const chat = useCopilotChat({
        // ...
        makeSystemMessage: myCustomMakeSystemMessage,
      });

      return (
        <div>
          {/* Render your custom UI using visibleMessages */}
        </div>
      );
    };
    ```
  </Tab>
</Tabs>

