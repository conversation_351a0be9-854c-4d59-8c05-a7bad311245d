---
title: "Quickstart"
description: "Get started with Copilot<PERSON>it in under 5 minutes."
icon: "lucide/Play"
---

import { LinkIcon } from "lucide-react";
import SelfHostingCopilotRuntimeCreateEndpoint from "@/snippets/self-hosting-copilot-runtime-create-endpoint.mdx";
import SelfHostingCopilotRuntimeConfigureCopilotKitProvider from "@/snippets/self-hosting-copilot-runtime-configure-copilotkit-provider.mdx";
import ConnectCopilotUI from "@/snippets/copilot-ui.mdx";
import CopilotCloudConfigureCopilotKitProvider from "@/snippets/copilot-cloud-configure-copilotkit-provider.mdx";
import CloudCopilotKitProvider from "@/snippets/cloud/cloud-copilotkit-provider.mdx";
import {
  TailoredContent,
  TailoredContentOption,
} from "@/components/react/tailored-content.tsx";
import { FaCloud, FaServer } from "react-icons/fa";

## Using the CLI
If you have a **NextJS** application, you can use our CLI to automatically bootstrap your application for use with CopilotKit.

```bash
npx copilotkit@latest init
```

<Accordions>
  <Accordion title="Starting from scratch?">
    No problem! Just use `create-next-app` to make a new NextJS application quickly.
    ```bash
    npx create-next-app@latest
    ```
  </Accordion>
</Accordions>

## Code-along
If you don't have a NextJS application or just want to code-along, you can follow the steps below.

<TailoredContent id="copilot-hosting">
<TailoredContentOption
  id="copilot-cloud"
  title="Copilot Cloud (Recommended)"
  description="Use our hosted backend endpoint to get started quickly (OpenAI only)."
  icon={<FaCloud />}
>

<Steps>
<Step>
### Install CopilotKit

First, install the latest packages for CopilotKit.

```package-install
npm install @copilotkit/react-ui @copilotkit/react-core
```

</Step>
<Step>
### Get a Copilot Cloud Public API Key
Navigate to [Copilot Cloud](https://cloud.copilotkit.ai) and follow the instructions to get a public API key - it's free!
</Step>
<Step>
### Setup the CopilotKit Provider

The [`<CopilotKit>`](/reference/components/CopilotKit) component must wrap the Copilot-aware parts of your application. For most use-cases, 
it's appropriate to wrap the CopilotKit provider around the entire app, e.g. in your layout.tsx.

<CloudCopilotKitProvider components={props.components} />

</Step>
<Step>
### Choose a Copilot UI

You are almost there! Now it's time to setup your Copilot UI.

<ConnectCopilotUI components={props.components} />

</Step>
</Steps>
</TailoredContentOption>
<TailoredContentOption
  id="self-hosted"
  title="Self-hosting"
  description="Learn to host CopilotKit's runtime yourself with your own backend."
  icon={<FaServer />}
>

<Steps>
<Step>
### Install CopilotKit
First, install the latest packages for CopilotKit.

```package-install
npm install @copilotkit/react-ui @copilotkit/react-core @copilotkit/runtime
```
</Step>
<Step>
### Set up a Copilot Runtime Endpoint

<SelfHostingCopilotRuntimeCreateEndpoint components={props.components} />
</Step>
<Step>
### Configure the CopilotKit Provider

<SelfHostingCopilotRuntimeConfigureCopilotKitProvider
  components={props.components}
/>

</Step>
<Step>
### Choose a Copilot UI

You are almost there! Now it's time to setup your Copilot UI.

<ConnectCopilotUI components={props.components} />

</Step>
</Steps>
</TailoredContentOption>
</TailoredContent>

---

## Next Steps

🎉 Congrats! You've successfully integrated a fully functional chatbot in your application! Give it a try now and see it in action. Want to
take it further? Learn more about what CopilotKit has to offer!

<Cards>
  <Card
    title="Connecting Your Data"
    description="Learn how to connect CopilotKit to your data, application state and user state."
    href="/guides/connect-your-data"
    icon={<LinkIcon />}
  />
  <Card
    title="Generative UI"
    description="Learn how to render custom UI components directly in the CopilotKit chat window."
    href="/guides/generative-ui"
    icon={<LinkIcon />}
  />
  <Card
    title="Frontend Actions"
    description="Learn how to allow your copilot to take applications on frontend."
    href="/guides/frontend-actions"
    icon={<LinkIcon />}
  />
  <Card
    title="CoAgents (LangGraph)"
    description="Check out our section about CoAgents, our approach to building agentic copilots and experiences."
    href="/coagents"
    icon={<LinkIcon />}
  />
</Cards>
