---
title: <PERSON><PERSON><PERSON>
---

To trace your LLM runs with <PERSON><PERSON><PERSON>, make sure to set up your environment variables:

```bash title=".env"
LANGCHAIN_API_KEY="<your-api-key>"
LANGCHAIN_PROJECT="<your-project-name>"
LANGCHAIN_TRACING_V2="true"
LANGCHAIN_ENDPOINT="https://api.smith.langchain.com""
```

Next, use the `LangChainAdapter` to trace your CopilotKit runs:

```tsx filename="route.ts" showLineNumbers {6-8}
const { LangChainAdapter } = await import("@copilotkit/runtime");
const { ChatOpenAI } = await import("@langchain/openai");

async function getLangChainOpenAIAdapter() {
  return new LangChainAdapter({
    chainFn: async ({ messages, tools, threadId }) => {
      const model = new ChatOpenAI({
        modelName: "gpt-4-1106-preview",
      }).bindTools(tools, {
        strict: true,
      });
      return model.stream(messages, {
        tools,
        metadata: { conversation_id: threadId },
      });
    },
  });
}
```

Note that `threadId` is passed to the model as `conversation_id` in the metadata.
