---
title: Overview
---
import { YouTubeVideo } from "@/components/react/youtube-video";
import { FaGithub } from "react-icons/fa";
import Link from "next/link";

# AI Todo List Copilot Tutorial

<div>
  <div>**Time to complete:** 5 minutes</div>
  <div>**Difficulty:** Easy</div>
</div>

<br />

<YouTubeVideo videoId="n1lJyaLam7g" defaultPlaybackRate={1.25} />

## What you'll learn

In this tutorial, you will take a simple todo list app and supercharge it with a copilot. You will learn:

- 💡 How to embed an in-app copilot with a chat UI
- 💡 How to use `useCopilotReadable` to allow your copilot to read the state of your app
- 💡 How to use `useCopilotAction` to allow your copilot to perform actions

## Try it out!

You can try out an interactive example of the end result below:

<Link href="https://github.com/CopilotKit/example-todos-app?ref=docs-tutorial" className="no-underline" target="_blank">
  <button className="bg-neutral-800 text-white px-4 py-2 rounded-lg flex items-center gap-2 mt-2 font-medium">
    <FaGithub className="w-4 h-4" />
    <span>View source code on GitHub</span>
  </button>
</Link>

<div className="flex flex-col shadow-lg rounded-lg my-4">
  <div className="bg-neutral-800 h-11 rounded-t-lg flex items-center px-4">
    <div className="flex-1 text-center text-white font-medium">CopilotKit Todo List Copilot Demo</div>
  </div>
  <iframe
    className="rounded-b-lg"
    src="https://example-todos-app.vercel.app/"
    width="100%"
    height="700"
    referrerPolicy="strict-origin-when-cross-origin"
    title="CopilotKit Todo List Copilot Demo"
    frameBorder="0"
    allowFullScreen
    ></iframe>
</div>

In the next step, we'll start building our copilot.