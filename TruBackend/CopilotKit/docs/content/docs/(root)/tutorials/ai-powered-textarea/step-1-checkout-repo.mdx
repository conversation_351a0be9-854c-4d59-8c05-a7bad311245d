---
title: "Step 1: Checkout the repo"
---

<Steps>
<Step>
### Checkout the repository
We'll begin by checking out the base code of the todo list app. We'll start from the `base-start-here` branch.

```shell
git clone -b base-start-here https://github.com/CopilotKit/example-textarea.git
cd example-textarea
```
</Step>
<Step>
### Install dependencies

To install the dependencies, run the following:

```shell
npm install
```
</Step>
<Step>
### Start the project

Now, you are ready to start the project by running:

```shell
npm run dev
```

You should be able to go to [http://localhost:3000](http://localhost:3000) and see the todo list app. Feel free to play around with the app to get a feel for it.
</Step>
</Steps>

Next, let's start adding some AI copilot superpowers to this app.