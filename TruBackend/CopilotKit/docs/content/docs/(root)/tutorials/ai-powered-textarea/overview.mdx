---
title: Overview
---
import { YouTubeVideo } from "@/components/react/youtube-video";
import { FaGithub } from "react-icons/fa";
import Link from "next/link";

<div>
  <div>**Time to complete:** 5 minutes</div>
  <div>**Difficulty:** Easy</div>
</div>

<br />

<YouTubeVideo videoId="C7px2wxr0k4" defaultPlaybackRate={1.25} />

## What you'll learn

In this tutorial, you will take a simple email application and add AI-powered autocompletion to it. The app is a simple email client, with a regular textarea used to compose an email. You're going to add CopilotKit to the app, so that the textarea provides relevant autocompletions as you type. The textarea will be aware of the full email history.

You will learn:

- 💡 How to use `useCopilotReadable` to allow your copilot to read the state of your app
- 💡 How to use the `<CopilotTextarea />` component to get instant context-aware autocompletions in your app
- 💡 How to use the Copilot Textarea Action Popup to generate text or adjust existing text in the textarea

## Try it out!

You can try out an interactive example of the end result below:

<Link href="https://github.com/CopilotKit/example-textarea?ref=docs-tutorial" className="no-underline" target="_blank">
  <button className="bg-neutral-800 text-white px-4 py-2 rounded-lg flex items-center gap-2 mt-2 font-medium">
    <FaGithub className="w-4 h-4" />
    <span>View source code on GitHub</span>
  </button>
</Link>

<div className="flex flex-col shadow-lg rounded-lg my-4">
  <div className="bg-neutral-800 h-11 rounded-t-lg flex items-center px-4">
    <div className="flex-1 text-center text-white font-medium">CopilotKit Todo List Copilot Demo</div>
  </div>
  <iframe
    className="rounded-b-lg h-[850px]"
    src="https://example-textarea.vercel.app/"
    width="100%"
    referrerPolicy="strict-origin-when-cross-origin"
    title="CopilotKit Textarea Demo"
    frameBorder="0"
    allowFullScreen
    ></iframe>
</div>

In the next step, we'll start building our copilot.