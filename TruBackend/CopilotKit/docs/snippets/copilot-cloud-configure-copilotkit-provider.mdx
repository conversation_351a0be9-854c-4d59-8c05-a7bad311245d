import { Callout } from 'fumadocs-ui/components/callout';
import { LinkToCopilotCloud } from '@/components/react/link-to-copilot-cloud';

The [`<CopilotKit>`](/reference/components/CopilotKit) provider must wrap the Copilot-aware parts of your application.
For most use-cases, it's appropriate to wrap the `CopilotKit` provider around the entire app, e.g. in your `layout.tsx`

<Callout type="info">
  Note that you can add the `<CopilotKit>` provider anywhere in your application. In fact, you can have multiple `<CopilotKit>` providers per app if you want independent copilots.
</Callout>


<Callout type="info">
    <LinkToCopilotCloud asButton={false}>Click here to get your Copilot Cloud API key for free</LinkToCopilotCloud>. Then, replace `<your-public-api-key>` with your actual API key.
</Callout>

```tsx title="layout.tsx" showLineNumbers
import "./globals.css";

import { ReactNode } from "react";
import { CopilotKit } from "@copilotkit/react-core"; // [!code highlight]

export default function RootLayout({ children }: { children: ReactNode }) {
    return (
      <html lang="en">
        <body> 
          {/* Use the public api key you got from Copilot Cloud  */} // [!code highlight:4]
          <CopilotKit publicApiKey="<your-copilot-cloud-public-api-key>"> 
            {children}
          </CopilotKit>
        </body>
      </html>
    );
}
```