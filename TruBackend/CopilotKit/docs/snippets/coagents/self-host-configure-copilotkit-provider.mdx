```tsx title="layout.tsx"
import "./globals.css";
import { ReactNode } from "react";
import { CopilotKit } from "@copilotkit/react-core"; // [!code highlight]

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en">
      <body> 
        {/* Use the public api key you got from Copilot Cloud  */} // [!code highlight:5]
        <CopilotKit 
          runtimeUrl="/api/copilotkit"
          agent="sample_agent" // the name of the agent you want to use
        > 
          {children}
        </CopilotKit>
      </body>
    </html>
  );
}
```