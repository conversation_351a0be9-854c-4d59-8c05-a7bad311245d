<Tabs groupId="python-pm" items={['Poetry', 'pip', 'conda']} default="Poetry">
    <Tab value="Poetry">
    ```bash
    poetry add copilotkit
    # including support for crewai
    poetry add copilotkit[crewai]
    ```
    </Tab>
    
    <Tab value="pip">

    ```bash
    pip install copilotkit --extra-index-url https://copilotkit.gateway.scarf.sh/simple/
    # including support for crewai
    pip install copilotkit[crewai] --extra-index-url https://copilotkit.gateway.scarf.sh/simple/
    ```
    </Tab>

    <Tab value="conda">
    ```bash
    conda install copilotkit -c copilotkit-channel
    # including support for crewai
    conda install copilotkit[crewai] -c copilotkit-channel
    ```
    </Tab>

</Tabs>
