<Tabs groupId="python-pm" items={['Poetry', 'pip', 'conda']} default="Poetry">
    <Tab value="Poetry">
    ```bash
    poetry add copilotkit
    ```
    </Tab>
    
    <Tab value="pip">

    ```bash
    pip install copilotkit --extra-index-url https://copilotkit.gateway.scarf.sh/simple/
    ```
    </Tab>

    <Tab value="conda">
    ```bash
    conda install copilotkit -c copilotkit-channel
    ```
    </Tab>
</Tabs>
