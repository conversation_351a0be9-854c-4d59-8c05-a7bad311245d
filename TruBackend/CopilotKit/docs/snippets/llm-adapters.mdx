import { Callout } from 'fumadocs-ui/components/callout';
import { TailoredContent, TailoredContentOption } from "@/components/react/tailored-content.tsx";
import SelfHostingCopilotRuntimeCreateEndpoint from "@/snippets/self-hosting-copilot-runtime-create-endpoint.mdx";
import { FaCloud, FaServer } from "react-icons/fa";
import FindYourCopilotRuntime from "@/snippets/find-your-copilot-runtime.mdx";

LLM Adapters are responsible for executing the request with the LLM and standardizing the request/response format in a way that the Copilot Runtime can understand.

Currently, we support the following LLM adapters natively:

- [OpenAI Adapter (Azure also supported)](/reference/classes/llm-adapters/OpenAIAdapter)
- [OpenAI Assistant Adapter](/reference/classes/llm-adapters/OpenAIAssistantAdapter)
- [LangChain Adapter](/reference/classes/llm-adapters/LangChainAdapter)
- [Groq Adapter](/reference/classes/llm-adapters/GroqAdapter)
- [Google Generative AI Adapter](/reference/classes/llm-adapters/GoogleGenerativeAIAdapter)
- [Anthropic Adapter](/reference/classes/llm-adapters/AnthropicAdapter)

<Callout type="info">
  You can use the [LangChain Adapter](/reference/classes/llm-adapters/LangChainAdapter) to use any LLM provider we don't yet natively support!
</Callout>


<Callout type="info">
  It's not too hard to write your own LLM adapter from scratch -- see the existing adapters for inspiration. And of course, we would love a contribution! ⭐️
</Callout>


<TailoredContent id="hosting">
  <TailoredContentOption
    id="copilot-cloud"
    title="Copilot Cloud (Recommended)"
    description="Use our hosted backend endpoint to get started quickly (OpenAI only)."
    icon={<FaCloud />}
  >
  Configure the used LLM adapter [on your Copilot Cloud dashboard](https://cloud.copilotkit.ai/)!

  </TailoredContentOption>

  <TailoredContentOption
    id="self-hosted"
    title="Self-hosting"
    description="Learn to host CopilotKit's runtime yourself with your own backend."
    icon={<FaServer />}
  >

  <Steps>
    <Step>
    ## Find your CopilotRuntime instance
    <FindYourCopilotRuntime components={props.components} />

    </Step>

    <Step>
    ## Modify the used LLM Adapter

    Use the example code below to tailor your CopilotRuntime instantiation to your desired llm adapter.

    <SelfHostingCopilotRuntimeCreateEndpoint components={props.components} />
    </Step>

    <Step>
    ## Make further customizations
    See the reference documentation linked above for further customization parameters.
    </Step>

  </Steps>



  </TailoredContentOption>
</TailoredContent>



