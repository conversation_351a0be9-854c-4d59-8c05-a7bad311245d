name: Changesets
description: A GitHub action to automate releases with Changesets
runs:
  using: "node20"
  main: "dist/index.js"
inputs:
  publish:
    description: "The command to use to build and publish packages"
    required: false
  version:
    description: "The command to update version, edit CHANGELOG, read and delete changesets. Default to `changeset version` if not provided"
    required: false
  cwd:
    description: Sets the cwd for the node process. Default to `process.cwd()`
    required: false
  commit:
    description: |
      The commit message. Default to `Version Packages`
    required: false
  title:
    description: The pull request title. Default to `Version Packages`
    required: false
  setupGitUser:
    description: Sets up the git user for commits as `"github-actions[bot]"`. Default to `true`
    required: false
    default: true
  createGithubReleases:
    description: "A boolean value to indicate whether to create Github releases after `publish` or not"
    required: false
    default: true
  branch:
    description: Sets the branch in which the action will run. Default to `github.ref_name` if not provided
    required: false
outputs:
  published:
    description: A boolean value to indicate whether a publishing is happened or not
  publishedPackages:
    description: >
      A JSON array to present the published packages. The format is `[{"name": "@xx/xx", "version": "1.2.0"}, {"name": "@xx/xy", "version": "0.8.9"}]`
  hasChangesets:
    description: A boolean about whether there were changesets. Useful if you want to create your own publishing functionality.
  pullRequestNumber:
    description: The pull request number that was created or updated
branding:
  icon: "package"
  color: "blue"
