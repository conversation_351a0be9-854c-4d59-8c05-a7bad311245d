name: Danger

on:
  pull_request:
    paths:
      - 'sdk-python/copilotkit/langgraph_agent.py'
      - 'CopilotKit/packages/runtime/src/lib/runtime/remote-lg-cloud-action.ts'

jobs:
  danger:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: "9.5"

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'
          cache-dependency-path: '**/pnpm-lock.yaml'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run Danger
        run: pnpm exec danger ci
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}