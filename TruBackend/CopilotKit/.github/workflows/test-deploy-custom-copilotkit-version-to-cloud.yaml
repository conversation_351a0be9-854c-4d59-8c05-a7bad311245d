name: Test Deploy Custom CopilotKit Version to Cloud

on:
  workflow_dispatch:
    inputs:
      version:
        description: "Version to deploy (e.g. `1.5.12-next.0`)"
        required: true

jobs:
  run:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - uses: actions/checkout@v3
        with:
          repository: copilotkit/copilotcloud
          ref: main
          sparse-checkout: '.github/actions'
          sparse-checkout-cone-mode: false
          path: .github/cloud-actions

      - name: LS LA
        run: |
          ls -la .github
          ls -la .github/cloud-actions

      # - name: Test Composite Action
      #   uses: copilotkit/copilotcloud/.github/workflows/test-deploy-custom-copilotkit-version-to-cloud.yml@main
      #   with:
      #     version: ${{ inputs.version }}
      #   secrets:
      #     aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
      #     aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
