name: Destroy Preview Environments

on:
  pull_request:
    types: [ closed ]
  workflow_dispatch:
    inputs:
      pr_number:
        description: 'Pull Request Number'
        required: true

concurrency:
  group: preview-${{ github.event.pull_request.number }}-destroy
  cancel-in-progress: false

jobs:
  preview_envs_destroy:
    if: github.event_name == 'pull_request' && contains(github.event.pull_request.labels.*.name, 'preview') || github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    name: Destroy Preview Environments
    permissions:
      deployments: write
      pull-requests: write
      statuses: write
      actions: write
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Install AWS CDK
        run: npm install -g aws-cdk

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Configure AWS Profile
        run: |
          aws configure set aws_access_key_id ${{ secrets.AWS_ACCESS_KEY_ID }} --profile default
          aws configure set aws_secret_access_key ${{ secrets.AWS_SECRET_ACCESS_KEY }} --profile default
          aws configure set region us-east-1
      
      - uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Infra install dependencies
        working-directory: infra
        run: pnpm i

      - name: Set variable UNIQUE_ENV_ID
        id: set_unique_env_id
        run: |
          echo "UNIQUE_ENV_ID=${{ github.event.number && format('PR{0}', github.event.number) || format('PR{0}', inputs.pr_number) }}" >> "$GITHUB_OUTPUT"

      - name: cdk destroy
        working-directory: ./infra
        run: cdk destroy --profile default --all --force
        env:
          UNIQUE_ENV_ID: ${{ steps.set_unique_env_id.outputs.UNIQUE_ENV_ID }}
          GITHUB_ACTIONS_RUN_ID: "${{ github.run_id }}"
          GITHUB_PR_NUMBER: "${{ github.event.number }}"

      - name: Comment post-destroy
        uses: thollander/actions-comment-pull-request@v3
        with:
          comment-tag: preview-status-update
          message: |
            Preview environments destroyed for this pull request.