name: Preview Environments

on:
  pull_request:
    branches:
      - main

concurrency:
  group: preview-${{ github.event.pull_request.number }}-deploy
  cancel-in-progress: false

jobs:
  build_images:
    if: ${{ !contains(github.event.pull_request.labels.*.name, 'automated-version-pr') }}
    runs-on: ubuntu-latest
    name: Build Images
    strategy:
      matrix:
        app:
          - {
              project: "coagents-research-canvas",
              type: "agent",
              runtime: "python",
              deps: "local-deps",
            }
          - {
              project: "coagents-research-canvas",
              type: "agent",
              runtime: "js",
              deps: "local-deps",
            }
          - { project: "coagents-research-canvas", type: "ui" }
          - {
              project: "coagents-routing",
              type: "agent",
              runtime: "python",
              deps: "local-deps",
            }
          - {
              project: "coagents-routing",
              type: "agent",
              runtime: "js",
              deps: "local-deps",
            }
          - { project: "coagents-routing", type: "ui" }
          - {
              project: "coagents-qa-text",
              type: "agent",
              runtime: "python",
              deps: "local-deps",
            }
          - {
              project: "coagents-qa-text",
              type: "agent",
              runtime: "js",
              deps: "local-deps",
            }
          - { project: "coagents-qa-text", type: "ui" }
          - {
              project: "coagents-qa-native",
              type: "agent",
              runtime: "python",
              deps: "local-deps",
            }
          - {
              project: "coagents-qa-native",
              type: "agent",
              runtime: "js",
              deps: "local-deps",
            }
          - { project: "coagents-qa-native", type: "ui" }
          - { project: "next-openai", type: "next-openai" }
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Configure AWS Profile
        run: |
          aws configure set aws_access_key_id ${{ secrets.AWS_ACCESS_KEY_ID }} --profile default
          aws configure set aws_secret_access_key ${{ secrets.AWS_SECRET_ACCESS_KEY }} --profile default
          aws configure set region us-east-1

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: AWS ECR Login
        run: |
          aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 058264219587.dkr.ecr.us-east-1.amazonaws.com

      - name: (UI) Setup pnpm
        if: matrix.app.type == 'ui' || matrix.app.type == 'next-openai'
        uses: pnpm/action-setup@v4
        with:
          version: "9.5"

      - name: (UI) Use Node.js 21
        if: matrix.app.type == 'ui' || matrix.app.type == 'next-openai'
        uses: actions/setup-node@v2
        with:
          node-version: 21.x

      - name: (UI) Install app dependencies
        if: matrix.app.type == 'ui'
        working-directory: examples/${{ matrix.app.project }}/${{ matrix.app.type }}
        run: pnpm install --frozen-lockfile

      - name: (UI, Local Deps) Install monorepo
        if: matrix.app.type == 'ui' || matrix.app.type == 'next-openai'
        working-directory: ./CopilotKit
        run: pnpm install --frozen-lockfile

      - name: (UI, Local Deps) Build @copilotkit packages
        if: matrix.app.type == 'ui' || matrix.app.type == 'next-openai'
        working-directory: ./CopilotKit
        run: pnpm run freshbuild

      - name: (UI, Local Deps) Link @copilotkit packages
        if: matrix.app.type == 'ui'
        working-directory: ./examples/${{ matrix.app.project }}/${{ matrix.app.type }}
        run: |
          for package in $(ls -d ../../../CopilotKit/packages/*); do
            pnpm link $package
          done

      - name: (UI) Build app
        if: matrix.app.type == 'ui'
        working-directory: examples/${{ matrix.app.project }}/${{ matrix.app.type }}
        run: pnpm run build
        env:
          OPENAI_API_KEY: placeholder

      - name: (next-openai) Build app
        if: matrix.app.type == 'next-openai'
        working-directory: CopilotKit/examples/next-openai
        run: pnpm run example-build
        env:
          OPENAI_API_KEY: placeholder

      - name: (UI) Build Image
        if: matrix.app.type == 'ui'
        run: |
          docker buildx build \
            -t 058264219587.dkr.ecr.us-east-1.amazonaws.com/coagents:${{ matrix.app.project }}-ui-${{ github.run_id }} \
            --build-arg APP_DIR=examples/${{ matrix.app.project }}/${{ matrix.app.type }} \
            -f ./examples/Dockerfile.${{ matrix.app.type }} \
            --platform=linux/amd64 \
            --provenance=false \
            --push \
            .
        env:
          DOCKER_BUILDKIT: 1

      - name: (next-openai) Build Image
        if: matrix.app.type == 'next-openai'
        run: |
          docker buildx build \
            -t 058264219587.dkr.ecr.us-east-1.amazonaws.com/coagents:next-openai-${{ github.run_id }} \
            -f ./CopilotKit/examples/next-openai/Dockerfile \
            --platform=linux/amd64 \
            --provenance=false \
            --push \
            .
        env:
          DOCKER_BUILDKIT: 1

      - name: (Agent) Build Image - Python Agent
        if: matrix.app.type == 'agent' && matrix.app.runtime == 'python'
        run: |
          docker buildx build \
            -t 058264219587.dkr.ecr.us-east-1.amazonaws.com/coagents:${{ matrix.app.project }}-agent-python-${{ matrix.app.deps }}-${{ github.run_id }} \
            --build-arg APP_DIR=examples/${{ matrix.app.project }}/agent \
            -f ./examples/Dockerfile.agent-python \
            --build-arg WITH_LOCAL_DEPS=${{ matrix.app.deps == 'local-deps' }} \
            --platform=linux/amd64 \
            --provenance=false \
            --push \
            .
        env:
          DOCKER_BUILDKIT: 1

      - name: (Agent) Build Image - JS Agent
        if: matrix.app.type == 'agent' && matrix.app.runtime == 'js'
        run: |
          docker buildx build \
            -t 058264219587.dkr.ecr.us-east-1.amazonaws.com/coagents:${{ matrix.app.project }}-agent-js-${{ matrix.app.deps }}-${{ github.run_id }} \
            --build-arg APP_DIR=examples/${{ matrix.app.project }}/agent-js \
            -f ./examples/Dockerfile.agent-js \
            --platform=linux/amd64 \
            --provenance=false \
            --push \
            .
        env:
          DOCKER_BUILDKIT: 1

  deploy:
    runs-on: ubuntu-latest
    name: Deploy Preview Environments
    needs: build_images
    outputs:
      e2e-urls: ${{ steps.export-e2e-urls.outputs.e2e-urls }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-region: us-east-1
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Set Environment Name
        id: set-environment-name
        run: |
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            echo "environment=pr-${{ github.event.pull_request.number }}" >> "$GITHUB_OUTPUT"
          elif [ "${{ github.event_name }}" = "push" ] && [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "environment=main" >> "$GITHUB_OUTPUT"
          fi

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: "9.5"

      - name: Use Node.js 21
        uses: actions/setup-node@v2
        with:
          node-version: 21.x

      - name: Infra install dependencies
        working-directory: infra
        run: pnpm i

      - name: Install AWS CDK
        run: npm install -g aws-cdk

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Configure AWS Profile
        run: |
          aws configure set aws_access_key_id ${{ secrets.AWS_ACCESS_KEY_ID }} --profile default
          aws configure set aws_secret_access_key ${{ secrets.AWS_SECRET_ACCESS_KEY }} --profile default
          aws configure set region us-east-1

      - name: cdk deploy
        working-directory: ./infra
        run: cdk deploy --profile default --all --require-approval never --outputs-file=cdk_outputs.json --concurrency 10
        env:
          UNIQUE_ENV_ID: "PR${{ github.event.number }}"
          GITHUB_ACTIONS_RUN_ID: "${{ github.run_id }}"
          GITHUB_PR_NUMBER: "${{ github.event.number }}"

      - name: Print CDK Outputs
        working-directory: infra
        run: |
          cat cdk_outputs.json

      - name: Generate table from outputs
        id: generate-table
        working-directory: infra
        run: |
          node generate-table
        env:
          GITHUB_SHA: ${{ github.sha }}

      - name: Delete a comment
        if: github.event_name == 'pull_request'
        uses: thollander/actions-comment-pull-request@v3
        with:
          comment-tag: preview-status-update
          mode: delete

      - name: Comment post-deployment
        if: github.event_name == 'pull_request'
        uses: thollander/actions-comment-pull-request@v3
        with:
          comment-tag: preview-status-update
          file-path: ./infra/preview-comment.md

      - name: Export Variable - E2E URLs
        id: export-e2e-urls
        working-directory: infra
        run: |
          echo "e2e-urls=$(node get-e2e-tests-urls.js | base64 -w 0)" >> "$GITHUB_OUTPUT"

      - name: Add preview label
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            github.rest.issues.addLabels({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ['preview']
            })

  test:
    if: github.event_name == 'pull_request'
    name: End to End Tests
    uses: ./.github/workflows/e2e.yml
    needs: deploy
    with:
      ref: "${{ github.event.pull_request.head.ref }}"
      sha: "${{ github.event.pull_request.head.sha }}"
      urls: "${{ needs.deploy.outputs.e2e-urls }}"
      pr_number: "${{ github.event.pull_request.number }}"
    secrets: inherit
