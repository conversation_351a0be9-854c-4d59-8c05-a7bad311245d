name: End to End Tests

on:
  workflow_call:
    inputs:
      ref:
        type: string
        required: true
      sha:
        type: string
        required: false
      urls:
        type: string
        required: false
      pr_number:
        type: number
        required: true

jobs:
  e2e_tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    permissions:
      checks: write
      contents: read
      pull-requests: write
      statuses: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}

      - name: Save E2E URLs
        working-directory: examples/e2e
        run: |
          echo "${{ inputs.urls }}" | base64 -d > app-configs.json

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "22"
          cache: "pnpm"
          cache-dependency-path: "./examples/e2e/pnpm-lock.yaml"

      - name: Install Dependencies
        working-directory: ./examples/e2e
        run: |
          pnpm install
          pnpm exec playwright install --with-deps

      - name: Print app configs
        working-directory: examples/e2e
        run: |
          cat app-configs.json

      - name: Delete a comment
        uses: thollander/actions-comment-pull-request@v3
        with:
          pr-number: ${{ inputs.pr_number }}
          comment-tag: test-run-status-update
          mode: delete

      - name: Comment before running tests
        uses: thollander/actions-comment-pull-request@v3
        with:
          pr-number: ${{ inputs.pr_number }}
          comment-tag: test-run-status-update
          message: |
            Running tests for commit ${{ inputs.sha }}...

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Configure AWS Profile
        run: |
          aws configure set aws_access_key_id ${{ secrets.AWS_ACCESS_KEY_ID }} --profile default
          aws configure set aws_secret_access_key ${{ secrets.AWS_SECRET_ACCESS_KEY }} --profile default
          aws configure set region us-east-1

      - name: Run Tests
        id: run_tests
        working-directory: ./examples/e2e
        run: |
          pnpm playwright test
          echo "::set-output name=exit_code::$?"
        env:
          CI: true
          GITHUB_SHA: ${{ github.sha }}
          COPILOT_CLOUD_PROD_RUNTIME_URL: "https://api.cloud.copilotkit.ai/copilotkit/v1"
          COPILOT_CLOUD_PROD_PUBLIC_API_KEY: ${{ secrets.COPILOT_CLOUD_PROD_PUBLIC_API_KEY }}
          COPILOT_CLOUD_STAGING_RUNTIME_URL: "https://api.cloud.stagingcopilotkit.ai/copilotkit/v1"
          COPILOT_CLOUD_STAGING_PUBLIC_API_KEY: ${{ secrets.COPILOT_CLOUD_STAGING_PUBLIC_API_KEY }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          GITHUB_ACTIONS_RUN_ID: ${{ github.run_id }}

      - name: Comment post-deployment
        if: always()
        uses: thollander/actions-comment-pull-request@v3
        with:
          pr-number: ${{ inputs.pr_number }}
          comment-tag: test-run-status-update
          file-path: ./examples/e2e/test-results/test-run-comment.md