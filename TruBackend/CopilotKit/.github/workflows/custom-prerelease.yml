name: Custom Tag Pre-Release

on:
  workflow_dispatch:

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    name: Pre-release
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: "Copilot<PERSON>it"
    timeout-minutes: 10
    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@v3

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: "9.5"

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          registry-url: https://registry.npmjs.org
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Install Dependencies
        run: pnpm i

      - name: Build
        run: pnpm run build

      - name: Publish snapshot
        run: ./scripts/release/publish-snapshot-release.sh
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}